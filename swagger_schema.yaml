openapi: 3.0.3
info:
  title: MedStata API
  description: API для работы с медицинскими данными в приложении MedStata
  version: 1.0.0
  contact:
    name: MedStata Support
    email: <EMAIL>

servers:
  - url: https://medstata.avpuser.ru
    description: Development server
  - url: https://medstata.avpuser.ru
    description: Production server

paths:
  /api/patients:
    get:
      summary: Получить данные пациента
      description: Возвращает данные авторизованного пациента
      tags:
        - Patients
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Данные пациента успешно получены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PatientResponse'
        '401':
          description: Не авторизован
        '404':
          description: Пациент не найден

  /api/lab-tests:
    get:
      summary: Получить лабораторные анализы
      description: Возвращает все лабораторные анализы авторизованного пользователя
      tags:
        - Lab Tests
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Анализы успешно получены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupedLabTestsResponse'
        '401':
          description: Не авторизован
        '404':
          description: Анализы не найдены

  /api/lab-tests/{id}:
    get:
      summary: Получить конкретный анализ с историей
      description: Возвращает детальную информацию об анализе включая историю
      tags:
        - Lab Tests
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID анализа
          schema:
            type: string
            example: "30e9de0eec814774e7901dbe10e23d943a3a66d57a1c5f18210165994f9ae49e"
      responses:
        '200':
          description: Детали анализа успешно получены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LabTestDetailResponse'
        '401':
          description: Не авторизован
        '404':
          description: Анализ не найден

  /api/lab-reports/upload:
    post:
      summary: Загрузить файл с результатами анализов
      description: Загружает файл с результатами лабораторных анализов для обработки
      tags:
        - Lab Reports
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Файл с результатами анализов (PDF, JPG, PNG)
                description:
                  type: string
                  description: Описание файла (необязательно)
                  example: "Результаты анализов от 15.01.2025"
              required:
                - file
      responses:
        '200':
          description: Файл успешно загружен и обработан
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadResponse'
        '400':
          description: Неверный формат файла или размер
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Не авторизован
        '413':
          description: Файл слишком большой
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Ошибка сервера при обработке файла
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/lab-reports:
    get:
      summary: Получить отчеты лабораторных анализов
      description: Возвращает все отчеты лабораторных анализов авторизованного пользователя
      tags:
        - Lab Reports
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Отчеты успешно получены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LabReportsResponse'
        '401':
          description: Не авторизован
        '404':
          description: Отчеты не найдены

  /api/lab-reports/{report_id}:
    get:
      summary: Получить конкретный лабораторный отчет
      description: Возвращает детальную информацию о конкретном лабораторном отчете включая все анализы из этого отчета
      tags:
        - Lab Reports
      security:
        - BearerAuth: []
      parameters:
        - name: report_id
          in: path
          required: true
          description: ID лабораторного отчета
          schema:
            type: string
            example: "5e135ac2227a83b2b7e2bf3df8be30258a75a9520989cd7f4abcd0ff0ae0a4ac"
      responses:
        '200':
          description: Детали отчета успешно получены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LabReportDetailResponse'
        '401':
          description: Не авторизован
        '404':
          description: Отчет не найден

components:
  schemas:
    PatientResponse:
      type: object
      required:
        - status
        - data
      properties:
        status:
          type: string
          example: "ok"
        data:
          $ref: '#/components/schemas/Patient'

    Patient:
      type: object
      required:
        - age
        - gender
      properties:
        age:
          type: integer
          description: Возраст пациента
          example: 30
        gender:
          type: string
          enum: [MALE, FEMALE]
          description: Пол пациента
          example: "MALE"

    GroupedLabTestsResponse:
      type: object
      required:
        - status
        - data
      properties:
        status:
          type: string
          example: "ok"
        data:
          $ref: '#/components/schemas/GroupedLabTests'

    GroupedLabTests:
      type: object
      required:
        - totalCount
        - normalCount
        - abnormalCount
        - otherCount
        - groupLabTests
      properties:
        totalCount:
          type: integer
          description: Общее количество анализов
          example: 25
        normalCount:
          type: integer
          description: Количество нормальных анализов
          example: 20
        abnormalCount:
          type: integer
          description: Количество анализов с отклонениями
          example: 3
        otherCount:
          type: integer
          description: Количество прочих анализов
          example: 2
        groupLabTests:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/LabTest'
          description: Анализы, сгруппированные по категориям
          example:
            "Биохимия крови":
              - id: "test1"
                name: "Глюкоза"
                status: "NORMAL"
            "Общий анализ крови":
              - id: "test2"
                name: "Гемоглобин"
                status: "ELEVATED"

    LabTest:
      type: object
      required:
        - id
        - name
        - status
        - performedAt
        - value
      properties:
        id:
          type: string
          description: Уникальный ID анализа
          example: "30e9de0eec814774e7901dbe10e23d943a3a66d57a1c5f18210165994f9ae49e"
        loincCode:
          type: string
          description: LOINC код анализа
          example: "33747-0"
        name:
          type: string
          description: Название анализа
          example: "Глюкоза"
        status:
          type: string
          enum: [NORMAL, ELEVATED, LOW, ABNORMAL, OTHER]
          description: Статус анализа
          example: "NORMAL"
        interpretationReasons:
          type: array
          items:
            type: string
          description: Причины интерпретации
          example: ["В пределах нормы"]
        performedAt:
          type: string
          format: date-time
          description: Дата выполнения анализа
          example: "2025-01-15T10:30:00Z"
        value:
          $ref: '#/components/schemas/LabTestValue'

    LabTestValue:
      type: object
      required:
        - value
        - unitLabel
      properties:
        value:
          type: string
          description: Значение анализа
          example: "5.2"
        referenceRange:
          type: string
          description: Референсный диапазон
          example: "3.9-6.1"
        unitLabel:
          type: string
          description: Единица измерения
          example: "ммоль/л"

    LabTestDetailResponse:
      type: object
      required:
        - status
        - data
      properties:
        status:
          type: string
          example: "ok"
        data:
          $ref: '#/components/schemas/LabTestDetail'

    LabTestDetail:
      type: object
      required:
        - current
        - history
      properties:
        current:
          $ref: '#/components/schemas/LabTest'
        history:
          type: array
          items:
            $ref: '#/components/schemas/LabTest'
          description: История аналогичных анализов

    FileUploadResponse:
      type: object
      required:
        - status
        - data
      properties:
        status:
          type: string
          example: "ok"
        message:
          type: string
          description: Сообщение о результате загрузки
          example: "Файл успешно загружен и обработан"
        data:
          type: object
          properties:
            fileId:
              type: string
              description: Уникальный ID загруженного файла
              example: "file_123456789"
            fileName:
              type: string
              description: Имя загруженного файла
              example: "lab_results_2025_01_15.pdf"
            fileSize:
              type: integer
              description: Размер файла в байтах
              example: 1048576
            uploadedAt:
              type: string
              format: date-time
              description: Время загрузки файла
              example: "2025-01-15T10:30:00Z"
            processingStatus:
              type: string
              enum: [PENDING, PROCESSING, COMPLETED, FAILED]
              description: Статус обработки файла
              example: "PENDING"
            extractedTestsCount:
              type: integer
              description: Количество извлеченных анализов (если обработка завершена)
              example: 12

    LabReportsResponse:
      type: object
      required:
        - status
        - data
      properties:
        status:
          type: string
          example: "ok"
        data:
          type: array
          items:
            $ref: '#/components/schemas/LabReport'

    LabReportDetailResponse:
      type: object
      required:
        - status
        - data
      properties:
        status:
          type: string
          example: "ok"
        data:
          $ref: '#/components/schemas/LabReportDetail'

    LabReport:
      type: object
      required:
        - id
        - performedAt
        - reportStatus
        - testCount
        - createdAt
      properties:
        id:
          type: string
          description: Уникальный ID отчета
          example: "5e135ac2227a83b2b7e2bf3df8be30258a75a9520989cd7f4abcd0ff0ae0a4ac"
        analysisName:
          type: string
          description: Название анализа
          example: "Биохимические исследования крови"
        performedAt:
          type: string
          format: date
          description: Дата выполнения анализа
          example: "2025-03-28"
        createdAt:
          type: string
          format: date-time
          description: Дата и время добавления отчета в систему
          example: "2025-03-28T14:30:00Z"
        specimenMaterial:
          type: string
          description: Материал образца
          example: "сыворотка крови"
        reportStatus:
          type: string
          enum: [PENDING, PROCESSING, DONE, ERROR]
          description: Статус обработки отчета
          example: "DONE"
        testCount:
          type: integer
          description: Количество анализов в отчете
          example: 12
        clinic:
          type: object
          properties:
            name:
              type: string
              example: "КДЛ"
        laboratory:
          type: object
          properties:
            name:
              type: string
              example: "Инвитро AstraLab"
            analysisEquipment:
              type: string
              example: "Sapphire 400 Premium"
        doctor:
          type: object
          properties:
            name:
              type: string
              example: "Альтшулер Б. Ю."

    LabReportDetail:
      type: object
      required:
        - report
        - tests
      properties:
        report:
          $ref: '#/components/schemas/LabReport'
        tests:
          type: array
          items:
            $ref: '#/components/schemas/LabTest'
          description: Все анализы из данного отчета

    ErrorResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: string
          example: "error"
        message:
          type: string
          description: Описание ошибки
          example: "Неподдерживаемый формат файла"
        code:
          type: string
          description: Код ошибки
          example: "INVALID_FILE_FORMAT"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []
