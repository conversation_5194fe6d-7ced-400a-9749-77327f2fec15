#!/bin/bash

echo "Running $0"

set -e

APP_NAME="MedStata"
ENV="PROD"
PUBSPEC_FILE="pubspec.yaml"
IPA_NAME="medstata_app.ipa"
IPA_PATH="build/ios/ipa/$IPA_NAME"

# API credentials
API_KEY="3C6Z237DTQ"
API_ISSUER_ID="4c2bcdee-6639-4ca4-9c2f-1c3b10fca46c"

# 🔢 Step 0: Auto-increment build number in pubspec.yaml
echo "🔢 Step 0: Incrementing build number in $PUBSPEC_FILE..."

CURRENT_VERSION=$(grep '^version:' "$PUBSPEC_FILE" | awk '{print $2}')
BASE_VERSION=$(echo "$CURRENT_VERSION" | cut -d+ -f1)
BUILD_NUMBER=$(echo "$CURRENT_VERSION" | cut -d+ -f2)

if [ -z "$BUILD_NUMBER" ]; then
  BUILD_NUMBER=1
else
  BUILD_NUMBER=$((BUILD_NUMBER + 1))
fi

NEW_VERSION="$BASE_VERSION+$BUILD_NUMBER"
sed -i '' "s/^version:.*/version: $NEW_VERSION/" "$PUBSPEC_FILE"

echo "✅ Updated version to $NEW_VERSION"

# 🔧 Step 1: Build
echo "🔧 Step 1: Building IPA for ENV=$ENV..."

flutter build ipa --release --dart-define=ENV=$ENV

echo "✅ Flutter build completed: $IPA_PATH"

# 🔐 Step 2: Upload to TestFlight
echo "🔐 Step 2: Uploading to TestFlight..."

if [ ! -f "$IPA_PATH" ]; then
  echo "❌ IPA-файл не найден: $IPA_PATH"
  exit 1
fi

echo "⚙️ Running upload with xcrun altool..."

xcrun altool --upload-app \
  --type ios \
  -f "$IPA_PATH" \
  --apiKey "$API_KEY" \
  --apiIssuer "$API_ISSUER_ID" \
  --verbose

echo "🚀 Upload completed successfully!"

# 🔗 Final message
echo "📦 Manage the build here:"
echo "👉 https://appstoreconnect.apple.com/apps/6749211879/testflight/ios"

exit 0