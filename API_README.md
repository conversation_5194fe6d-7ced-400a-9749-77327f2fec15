# MedStata API Client для Flutter

Этот проект содержит классы и сервисы для работы с MedStata API, включая получение данных пациентов и лабораторных анализов.

## Структура проекта

```
lib/
├── models/                 # Модели данных
│   ├── api_response.dart   # Базовые модели API ответов
│   ├── patient.dart        # Модель пациента
│   └── lab_test.dart       # Модели лабораторных анализов
├── services/               # Сервисы для работы с API
│   ├── api_client.dart     # HTTP клиент
│   ├── patient_service.dart # Сервис пациентов
│   └── lab_test_service.dart # Сервис лабораторных анализов
├── examples/               # Примеры использования
│   └── api_usage_example.dart
└── medstata_api.dart       # Главный файл экспорта
```

## Установка

1. Добавьте зависимость `http` в ваш `pubspec.yaml`:
```yaml
dependencies:
  http: ^1.4.0
```

2. Запустите команду:
```bash
flutter pub get
```

## Использование

### Импорт

```dart
import 'package:medstata_app/medstata_api.dart';
```

### Получение данных пациента

```dart
final patientService = PatientService();

try {
  final response = await patientService.getPatient('52952911');
  
  if (response.isSuccess) {
    final patient = response.data!;
    print('Возраст: ${patient.age}');
    print('Пол: ${patient.gender.value}');
  } else if (response.isUserNotFound) {
    print('Пользователь не найден');
  } else if (response.isNoData) {
    print('Нет данных пациента');
  }
} catch (e) {
  print('Ошибка: $e');
} finally {
  patientService.dispose();
}
```

### Получение лабораторных анализов

```dart
final labTestService = LabTestService();

try {
  final response = await labTestService.getLabTests('52952911');
  
  if (response.isSuccess) {
    final data = response.data!;
    print('Всего анализов: ${data.totalCount}');
    print('Нормальных: ${data.normalCount}');
    print('С отклонениями: ${data.abnormalCount}');
    
    // Получение анализов по категориям
    for (final category in data.groupLabTests.keys) {
      final tests = data.groupLabTests[category]!;
      print('$category: ${tests.length} анализов');
    }
  }
} catch (e) {
  print('Ошибка: $e');
} finally {
  labTestService.dispose();
}
```

### Получение лабораторных отчетов

```dart
final labReportService = LabReportService();

try {
  final response = await labReportService.getLabReports();

  if (response.isSuccess) {
    final reports = response.data!;
    print('Всего отчетов: ${reports.length}');

    for (final report in reports) {
      print('Отчет: ${report.filename}');
      print('Статус: ${report.status.displayName}');
      print('Анализов: ${report.testsCount}');
      print('Загружен: ${report.formattedUploadDate}');
    }

    // Получение статистики отчетов
    final stats = await labReportService.getLabReportsStatistics();
    if (stats != null) {
      print('Завершенных отчетов: ${stats['completed']}');
      print('В обработке: ${stats['processing']}');
    }
  }
} catch (e) {
  print('Ошибка: $e');
} finally {
  labReportService.dispose();
}
```

### Получение детальной информации об отчете

```dart
final labReportService = LabReportService();

try {
  const reportId = '5e135ac2227a83b2b7e2bf3df8be30258a75a9520989cd7f4abcd0ff0ae0a4ac';
  final response = await labReportService.getLabReportDetail(reportId);

  if (response.isSuccess) {
    final detail = response.data!;
    final report = detail.report;
    final tests = detail.tests;

    print('Отчет: ${report.analysisName ?? 'Без названия'}');
    print('Дата: ${report.formattedPerformedDate}');
    print('Статус: ${report.status.displayName}');
    print('Лаборатория: ${report.laboratoryName ?? 'Не указана'}');
    print('Анализов в отчете: ${tests.length}');

    // Статистика анализов
    final stats = detail.getTestsStatistics();
    print('Нормальных: ${stats['normal']}');
    print('С отклонениями: ${stats['abnormal']}');

    // Анализы по статусам
    final abnormalTests = detail.getTestsByStatus(LabTestStatus.abnormal);
    for (final test in abnormalTests) {
      print('⚠️ ${test.name}: ${test.value.value} ${test.value.unitLabel}');
    }
  }
} catch (e) {
  print('Ошибка: $e');
} finally {
  labReportService.dispose();
}
```

### Получение конкретного анализа с историей

```dart
final labTestService = LabTestService();

try {
  final response = await labTestService.getLabTest('test_id');
  
  if (response.isSuccess) {
    final detail = response.data!;
    final current = detail.current;
    
    print('Анализ: ${current.name}');
    print('Значение: ${current.value.value} ${current.value.unitLabel}');
    print('Статус: ${current.status.value}');
    
    // История анализов
    for (final historyItem in detail.history) {
      print('${historyItem.performedAt}: ${historyItem.value.value}');
    }
  }
} catch (e) {
  print('Ошибка: $e');
} finally {
  labTestService.dispose();
}
```

## API Endpoints

### Пациенты
- `GET /api/patients?userId={userId}` - получение данных пациента

### Лабораторные анализы
- `GET /api/lab-tests?userId={userId}` - получение всех анализов пользователя
- `GET /api/lab-tests/{id}` - получение конкретного анализа с историей

### Лабораторные отчеты
- `GET /api/lab-reports` - получение всех отчетов пользователя
- `GET /api/lab-reports/{report_id}` - получение конкретного отчета с анализами
- `POST /api/lab-reports/upload` - загрузка файла с результатами анализов

## Модели данных

### Patient
```dart
class Patient {
  final int age;
  final Gender gender;
}
```

### LabTest
```dart
class LabTest {
  final String id;
  final String? loincCode;
  final String name;
  final LabTestStatus status;
  final List<String> interpretationReasons;
  final String performedAt;
  final LabTestValue value;
}
```

### LabTestValue
```dart
class LabTestValue {
  final String value;
  final String? referenceRange;
  final String unitLabel;
}
```

### LabReport
```dart
class LabReport {
  final String id;
  final String? analysisName;
  final String performedAt;
  final String? specimenMaterial;
  final LabReportStatus status;
  final int testsCount;
  final String? clinicName;
  final String? laboratoryName;
  final String? doctorName;
}
```

### LabReportDetail
```dart
class LabReportDetail {
  final LabReport report;
  final List<LabTest> tests;
}
```

## Enums

### Gender
- `MALE` - мужской
- `FEMALE` - женский
- `OTHER` - другой

### LabTestStatus
- `NORMAL` - нормальный
- `ELEVATED` - повышенный
- `LOW` - пониженный
- `ABNORMAL` - отклонение от нормы
- `OTHER` - другой

## Обработка ошибок

Все сервисы могут выбрасывать `ApiException` с описанием ошибки:

```dart
try {
  final response = await patientService.getPatient(userId);
} on ApiException catch (e) {
  print('API ошибка: ${e.message}');
  if (e.statusCode != null) {
    print('HTTP код: ${e.statusCode}');
  }
} catch (e) {
  print('Неизвестная ошибка: $e');
}
```

## Тестирование

Запуск тестов:
```bash
flutter test test/api_test.dart
```

Запуск примера использования:
```bash
dart run lib/examples/api_usage_example.dart
```

## Конфигурация

По умолчанию API клиент подключается к `https://medstata.avpuser.ru. Для изменения базового URL:

```dart
final apiClient = ApiClient(baseUrl: 'https://api.medstata.com');
final patientService = PatientService(apiClient: apiClient);
```

## UI Экраны

### LabTestsScreen

Экран для отображения лабораторных анализов с красивым форматированием:

```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const LabTestsScreen(
      userId: '52952911',
    ),
  ),
);
```

Особенности экрана:
- **Сводная статистика** - общее количество анализов по статусам
- **Группировка по категориям** - анализы сгруппированы по медицинским категориям
- **Цветовая индикация статусов**:
  - 🟢 Зеленый - норма
  - 🔴 Оранжевый - требуют внимания (повышен/понижен/отклонение)
  - 🔵 Синий - прочее
- **Интерактивность** - нажатие на анализ открывает детальную информацию
- **Обновление данных** - кнопка обновления в AppBar

### LabTestDetailDialog

Диалог для отображения детальной информации об анализе:

```dart
showDialog(
  context: context,
  builder: (context) => LabTestDetailDialog(labTest: test),
);
```

Показывает:
- Текущий результат с референсным диапазоном
- Статус и интерпретацию
- Историю аналогичных анализов
- LOINC код

## Запуск приложения

1. Убедитесь, что API сервер запущен на `https://medstata.avpuser.ru`
2. Запустите Flutter приложение:

```bash
flutter run -d chrome --web-port=8081
```

3. Нажмите кнопку "Лабораторные анализы" на главном экране

## Тестирование

Запуск всех тестов:
```bash
flutter test
```

Запуск тестов экрана:
```bash
flutter test test/lab_tests_screen_test.dart
```

## Освобождение ресурсов

Не забывайте вызывать `dispose()` для освобождения ресурсов:

```dart
final patientService = PatientService();
final labTestService = LabTestService();

try {
  // Использование сервисов
} finally {
  patientService.dispose();
  labTestService.dispose();
}
```
