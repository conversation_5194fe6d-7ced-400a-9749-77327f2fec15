<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Auth</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #0088cc, #005580);
            color: white;
        }
        .container {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success {
            color: #4CAF50;
            font-size: 18px;
            margin-top: 20px;
        }
        .error {
            color: #f44336;
            font-size: 18px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔐 Авторизация через Telegram</h2>
        <div class="spinner"></div>
        <p id="status">Обработка данных авторизации...</p>
    </div>

    <script>
        function parseAuthData() {
            try {
                console.log('🔍 Parsing Telegram auth data...');
                console.log('Current URL:', window.location.href);
                console.log('Hash:', window.location.hash);
                console.log('Search:', window.location.search);

                let userData = {};
                let hasRequiredData = false;

                // 1. Сначала проверяем fragment (hash) - основной способ для Telegram
                const hash = window.location.hash;
                if (hash && hash.includes('tgAuthResult=')) {
                    console.log('🔍 Found tgAuthResult in hash');
                    try {
                        // Извлекаем base64 данные из fragment
                        const tgAuthResult = hash.split('tgAuthResult=')[1];
                        console.log('Base64 data:', tgAuthResult);
                        
                        // Декодируем base64
                        const decodedData = atob(tgAuthResult);
                        console.log('Decoded JSON:', decodedData);
                        
                        // Парсим JSON
                        userData = JSON.parse(decodedData);
                        console.log('✅ Parsed user data from hash:', userData);
                        
                        hasRequiredData = userData.id && userData.hash;
                    } catch (e) {
                        console.error('❌ Error parsing tgAuthResult:', e);
                    }
                }

                // 2. Если не нашли в hash, проверяем query параметры
                if (!hasRequiredData) {
                    console.log('🔍 Checking query parameters...');
                    const query = window.location.search.substring(1);
                    const params = new URLSearchParams(query);
                    console.log('Query string:', query);

                    // Собираем все параметры
                    for (const [key, value] of params.entries()) {
                        userData[key] = value;
                        console.log(`Parameter ${key}:`, value);
                    }
                    
                    hasRequiredData = userData.id && userData.hash;
                }

                // 3. Если не нашли в URL, проверяем fragment как query string
                if (!hasRequiredData && hash) {
                    console.log('🔍 Checking hash as query string...');
                    try {
                        const hashParams = new URLSearchParams(hash.substring(1));
                        for (const [key, value] of hashParams.entries()) {
                            userData[key] = value;
                            console.log(`Hash parameter ${key}:`, value);
                        }
                        hasRequiredData = userData.id && userData.hash;
                    } catch (e) {
                        console.log('⚠️ Could not parse hash as query string:', e);
                    }
                }

                // Проверяем наличие обязательных полей
                if (hasRequiredData) {
                    // Конвертируем числовые поля
                    if (typeof userData.id === 'string') {
                        userData.id = parseInt(userData.id);
                    }
                    if (userData.auth_date && typeof userData.auth_date === 'string') {
                        userData.auth_date = parseInt(userData.auth_date);
                    }

                    console.log('✅ Auth data parsed successfully:', userData);

                    // Сохраняем данные в различных местах для WebView
                    window.telegramAuthData = userData;

                    console.log('📋 Final user data to save:');
                    console.log('   ID:', userData.id);
                    console.log('   First Name:', userData.first_name);
                    console.log('   Last Name:', userData.last_name);
                    console.log('   Username:', userData.username);
                    console.log('   Photo URL:', userData.photo_url);
                    console.log('   Auth Date:', userData.auth_date);
                    console.log('   Hash:', userData.hash ? userData.hash.substring(0, 10) + '...' : 'null');

                    // Сохраняем в localStorage
                    try {
                        localStorage.setItem('telegram_auth_data', JSON.stringify(userData));
                        console.log('✅ Data saved to localStorage');
                    } catch (e) {
                        console.log('⚠️ Could not save to localStorage:', e);
                    }

                    // Отправляем postMessage для WebView
                    try {
                        if (window.parent && window.parent !== window) {
                            window.parent.postMessage(userData, '*');
                            console.log('✅ PostMessage sent to parent');
                        }

                        // Также пробуем отправить в webkit
                        if (window.webkit && window.webkit.messageHandlers) {
                            if (window.webkit.messageHandlers.telegramAuth) {
                                window.webkit.messageHandlers.telegramAuth.postMessage(userData);
                                console.log('✅ Message sent to webkit handler');
                            }
                        }
                    } catch (e) {
                        console.log('⚠️ Could not send postMessage:', e);
                    }

                    // Обновляем UI
                    document.getElementById('status').innerHTML = '✅ Авторизация успешна!<br>Возвращаемся в приложение...';
                    document.getElementById('status').className = 'success';

                    // Скрываем спиннер
                    document.querySelector('.spinner').style.display = 'none';

                    // Пытаемся закрыть окно через некоторое время
                    setTimeout(() => {
                        try {
                            window.close();
                        } catch (e) {
                            console.log('Could not close window:', e);
                        }
                    }, 2000);

                } else {
                    throw new Error('Отсутствуют обязательные параметры авторизации (id или hash)');
                }

            } catch (error) {
                console.error('❌ Error parsing auth data:', error);

                // Обновляем UI с ошибкой
                document.getElementById('status').innerHTML = '❌ Ошибка авторизации:<br>' + error.message;
                document.getElementById('status').className = 'error';

                // Скрываем спиннер
                document.querySelector('.spinner').style.display = 'none';
            }
        }

        // Запускаем парсинг когда страница загрузилась
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', parseAuthData);
        } else {
            parseAuthData();
        }

        // Также запускаем через небольшую задержку на всякий случай
        setTimeout(parseAuthData, 100);
    </script>
</body>
</html>
