# Решение проблем с MedStata API

## 🚨 Проблема: "Failed to fetch" в веб-версии

### Симптомы:
```
Ошибка при загрузке данных: ApiException: Неизвестная ошибка: 
ClientException: Failed to fetch, uri=https://medstata.avpuser.ru/api/lab-tests?userId=52952911
```

### Причина:
Браузеры блокируют HTTP запросы к `https://medstata.avpuser.ru` из-за политики CORS (Cross-Origin Resource Sharing).

### ✅ Решения:

#### 1. Использовать мобильные платформы (РЕКОМЕНДУЕТСЯ)

```bash
# iOS симулятор
flutter run -d "iPhone 16 Plus"

# Android эмулятор  
flutter run -d android

# Проверить доступные устройства
flutter devices
```

**Преимущества:**
- ✅ Работает из коробки
- ✅ Нет CORS ограничений
- ✅ Лучшая производительность
- ✅ Реальный мобильный опыт

#### 2. Запуск Chrome с отключенными CORS ограничениями

```bash
# macOS
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome \
  --args \
  --user-data-dir="/tmp/chrome_dev_test" \
  --disable-web-security \
  --disable-features=VizDisplayCompositor

# Windows
chrome.exe --user-data-dir="C:\temp\chrome_dev_test" --disable-web-security --disable-features=VizDisplayCompositor

# Linux
google-chrome --user-data-dir="/tmp/chrome_dev_test" --disable-web-security --disable-features=VizDisplayCompositor
```

Затем запустить Flutter:
```bash
flutter run -d chrome --web-port=8081
```

#### 3. Настройка прокси (для продакшена)

Добавить в `web/index.html`:
```html
<script>
  // Настройка прокси для API запросов
  if (window.location.hostname === 'localhost') {
    window.API_BASE_URL = 'https://medstata.avpuser.ru';
  } else {
    window.API_BASE_URL = '/api'; // Прокси на том же домене
  }
</script>
```

## 🔍 Отладка API запросов

### Включение логирования:

Логи уже включены в коде. В консоли вы увидите:

```
🔬 LabTestService.getLabTests called with userId: 52952911
🌐 API Request: GET https://medstata.avpuser.ru/api/lab-tests?userId=52952911
📋 Query Parameters: {userId: 52952911}
✅ API Response: 200
📄 Response Body: {"data":{"totalCount":63...
✅ LabTestService: API response received
```

### Проверка API вручную:

```bash
# Тест API из командной строки
dart run test_api_direct.dart

# Или через curl
curl "https://medstata.avpuser.ru/api/lab-tests?userId=52952911"
```

## 📱 Рекомендуемый workflow разработки:

1. **Разработка UI** - используйте iOS/Android симуляторы
2. **Тестирование API** - используйте `dart run test_api_direct.dart`
3. **Веб-версия** - только для демонстрации с отключенными CORS

## 🛠️ Дополнительные команды:

```bash
# Проверить доступные устройства
flutter devices

# Запустить эмуляторы
flutter emulators
flutter emulators --launch ios

# Горячая перезагрузка
# В терминале Flutter нажмите 'r' или 'R'

# Очистить кэш
flutter clean
flutter pub get

# Запустить тесты
flutter test
```

## ✅ Проверка работоспособности:

Если все работает правильно, вы должны увидеть:

1. **Главный экран** с кнопкой "Лабораторные анализы"
2. **Экран анализов** с:
   - 📊 Статистикой: 63 анализа (51 норма, 8 отклонений, 4 прочих)
   - 🔬 5 категорий анализов
   - 🎨 Цветовой индикацией статусов
3. **Детальный просмотр** при нажатии на анализ

## 🐛 Если проблемы остаются:

1. Убедитесь, что API сервер запущен на `https://medstata.avpuser.ru`
2. Проверьте, что `userId=52952911` существует в системе
3. Запустите `dart run test_api_direct.dart` для проверки API
4. Используйте мобильные платформы вместо веб-версии

## 📞 Поддержка:

Если проблема не решается:
1. Проверьте логи в консоли Flutter
2. Запустите тесты: `flutter test`
3. Убедитесь, что используете мобильную платформу для разработки
