# Экран лабораторных анализов - LabTestsScreen

## Описание

Создан красивый и функциональный экран для отображения лабораторных анализов пользователя с данными из API `https://medstata.avpuser.ru/api/lab-tests?userId=52952911`.

## Особенности экрана

### 🎨 Визуальное оформление

- **Современный Material Design** с использованием карточек и цветовых акцентов
- **Цветовая индикация статусов**:
  - 🟢 **Зеленый** - анализы в норме (✅)
  - 🔴 **Оранжевый** - требуют внимания (🔺 повышен, 🔻 понижен, 🔴 отклонение)
  - 🔵 **Синий** - прочие анализы (ℹ️)
- **Иконки и эмодзи** для лучшего восприятия информации
- **Адаптивный дизайн** с прокруткой для больших списков

### 📊 Структура данных

#### Сводная статистика (верхняя карточка):
```
📊 ИТОГО:
• Всего показателей: 63
• В норме: 51
• С отклонениями: 8
• Прочее: 4
```

#### Группировка по категориям:
1. **🔬 Биохимические исследования крови** (29 анализов)
2. **🔬 Гематологические исследования крови и плазмы** (28 анализов)
3. **🔬 Микробиологические исследования других жидкостей** (1 анализ)
4. **🔬 Прочие лабораторные исследования** (3 анализа)
5. **🔬 гормоны** (2 анализа)

### 🔧 Функциональность

- **Автоматическая загрузка** данных при открытии экрана
- **Кнопка обновления** в AppBar для перезагрузки данных
- **Обработка ошибок** с понятными сообщениями пользователю
- **Индикатор загрузки** во время получения данных
- **Интерактивные элементы** - нажатие на анализ открывает детальную информацию

### 📱 Интерактивность

#### Детальный просмотр анализа:
При нажатии на любой анализ открывается диалог `LabTestDetailDialog` с:
- **Текущим результатом** с референсным диапазоном
- **Статусом и интерпретацией** 
- **Историей аналогичных анализов**
- **LOINC кодом** для медицинской классификации

## Техническая реализация

### Файлы:
- `lib/screens/lab_tests_screen.dart` - основной экран
- `lib/widgets/lab_test_detail_dialog.dart` - диалог деталей анализа
- `test/lab_tests_screen_test.dart` - тесты экрана

### Используемые сервисы:
- `LabTestService.getLabTests(userId)` - получение всех анализов
- `LabTestService.getLabTest(testId)` - получение деталей конкретного анализа

### Модели данных:
- `GroupedLabTests` - сгруппированные анализы с статистикой
- `LabTest` - отдельный лабораторный анализ
- `LabTestDetail` - детальная информация с историей

## Пример отображения

```
════════════════════
🔬 Биохимические исследования крови

🟢 В норме:
✅️ SHBG (глобулин): 37.50 нмоль/л — /2942_1
✅️ АЛТ: 18.00 Ед/л — /1742_6
✅️ АСТ: 24.00 Ед/л — /1920_8
✅️ Глюкоза: 5.40 ммоль/л — /2345_7
...

🔴 Требуют внимания:
🔺 Аполипопротеин B: 1.29 г/л — /1884_6
🔺 Холестерин ЛПНП: 4.13 ммоль/л — /2089_1

════════════════════
🔬 Гематологические исследования крови и плазмы

🟢 В норме:
✅️ MCF (прочность сгустка): 50.00 мм — /52778_8
✅️ Гемоглобин: 14.80 г/дл — /718_7
...

🔴 Требуют внимания:
🔺 Лимфоциты (%): 43.70 % — /26478_8
🔻 Тромбоциты: 151.00 10⁹/л — /26515_7
```

## Запуск и тестирование

### Запуск приложения:

#### Для мобильных платформ (рекомендуется):
```bash
flutter run -d "iPhone 16 Plus"  # iOS симулятор
flutter run -d android           # Android эмулятор
```

#### Для веб-версии:
```bash
flutter run -d chrome --web-port=8081
```

**⚠️ Важно для веб-версии:** Из-за CORS ограничений браузера, запросы к `https://medstata.avpuser.ru` могут блокироваться.

**Решения:**
1. **Запустить Chrome с отключенными CORS ограничениями:**
   ```bash
   # macOS
   open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security --disable-features=VizDisplayCompositor

   # Затем запустить Flutter
   flutter run -d chrome --web-port=8081
   ```

2. **Использовать мобильные платформы** (iOS/Android симуляторы) - рекомендуется

3. **Настроить CORS на API сервере** (если у вас есть доступ к серверу)

### Запуск тестов:
```bash
flutter test test/lab_tests_screen_test.dart
```

### Навигация к экрану:
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const LabTestsScreen(),
  ),
);
```

---

# Экран документов лабораторных анализов - LabReportsScreen

## Описание

Создан новый экран для отображения загруженных документов с результатами лабораторных анализов. Экран интегрирован в LabTestsScreen как второй таб "Документы" и использует LabReportService для получения данных.

## Особенности экрана

### 🎨 Визуальное оформление

- **Современный дизайн карточек** с тенями и скругленными углами
- **Цветовая индикация статусов**:
  - 🟢 **Зеленый** - документ обработан (✅)
  - 🟠 **Оранжевый** - документ в обработке (⏳)
  - 🔴 **Красный** - ошибка обработки (❌)
- **Иконки документов** для лучшего восприятия
- **Кнопки действий** (удаление, поделиться) для завершенных документов

### 📊 Структура данных

Экран отображает данные из API `/api/lab-reports/` через LabReportService:

- **ID документа** - уникальный идентификатор
- **Имя файла** - оригинальное название загруженного файла
- **Дата загрузки** - когда документ был загружен
- **Статус обработки** - PENDING, PROCESSING, COMPLETED, FAILED
- **Количество анализов** - сколько анализов извлечено из документа
- **Размер файла** - размер в байтах с форматированием
- **Описание** - опциональное описание документа

### 🔧 Функциональность

#### Фильтрация документов:
- **Все документы** - показать все загруженные документы
- **Обработанные** - только успешно обработанные документы
- **В обработке** - документы, которые сейчас обрабатываются
- **С ошибками** - документы с ошибками обработки

#### Сортировка:
- **По дате добавления** - сначала новые документы
- **По названию** - алфавитная сортировка по имени файла
- **По статусу** - группировка по статусу обработки

#### Действия с документами:
- **Удаление** - удаление документа (только для завершенных)
- **Поделиться** - отправка документа (только для завершенных)

### 📱 Интеграция с LabTestsScreen

Экран интегрирован как второй таб в LabTestsScreen:

```dart
Widget _buildTabBar() {
  return Container(
    // Переключаемые табы "Биомаркеры" / "Документы"
    child: Row(
      children: [
        GestureDetector(
          onTap: () => setState(() => _selectedTabIndex = 0),
          child: // Таб "Биомаркеры"
        ),
        GestureDetector(
          onTap: () => setState(() => _selectedTabIndex = 1),
          child: // Таб "Документы"
        ),
      ],
    ),
  );
}
```

### 📁 Файлы:
- `lib/screens/lab_reports_screen.dart` - основной экран документов
- `lib/screens/lab_tests_screen.dart` - обновлен для поддержки табов
- `lib/examples/lab_reports_demo.dart` - демонстрационные данные
- `test/lab_reports_screen_test.dart` - тесты экрана

### 🔧 Используемые сервисы:
- `LabReportService.getLabReports()` - получение всех документов пользователя
- `LabReportService.getLabReportsByStatus()` - фильтрация по статусу
- `LabReportService.getLabReportsStatistics()` - статистика документов

### 📋 Модели данных:
- `LabReport` - модель документа с результатами анализов
- `LabReportStatus` - статус обработки документа
- `LabReportsResponse` - ответ API со списком документов

## Пример отображения

```
════════════════════
📄 Документы
════════════════════

[Все документы ▼]  [По дате добавления ▼]

┌─────────────────────────────────────┐
│ 📄  28 янв 2025                    ✅│
│     Хеликобактер антиген,          🗑️│
│     качественно                    📤│
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 📄  30 янв 2025                    ⏳│
│     Воздержание, эякулят и еще 26   │
│                                     │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 📄  15 апр 2025                    ✅│
│     Фагоцитоз, секрет простаты     🗑️│
│     качественно и еще              📤│
└─────────────────────────────────────┘
```

---

# Экран детального просмотра лабораторного отчета - LabReportDetailScreen

## Описание

Новый экран для детального просмотра конкретного лабораторного отчета. Открывается при нажатии на карточку отчета в экране "Документы" и использует API метод `/api/lab-reports/{report_id}` для получения полной информации об отчете включая все анализы.

## Особенности экрана

### 🎨 Визуальное оформление

- **Заголовок "Результаты"** с кнопками навигации и редактирования
- **Информационный блок** с данными профиля, даты, лаборатории
- **Переключаемые табы** "Результаты" / "Документ"
- **Список биомаркеров** с цветовой индикацией статуса
- **Современный дизайн** в стиле приложения

### 📊 Структура данных

Экран получает данные через `LabReportService.getLabReportDetail(reportId)`:

- **Информация об отчете** - LabReport с полными данными
- **Список анализов** - List<LabTest> со всеми биомаркерами из отчета
- **Статистика анализов** - количество нормальных/отклоненных результатов

### 🔧 Функциональность

#### Информационный блок:
- **Профиль** - аватар и имя пользователя (Alex)
- **Дата** - дата выполнения анализа в формате "28 янв 2025"
- **Лаборатория** - название лаборатории из отчета
- **Заметки** - переход к заметкам (TODO)

#### Табы:
- **Результаты** - список всех биомаркеров из отчета
- **Документ** - просмотр оригинального документа (TODO)

#### Биомаркеры:
- **Название анализа** - полное название биомаркера
- **Статус** - текстовое описание результата
- **Цветовая индикация**:
  - 🟢 **Зеленый** - нормальные значения
  - 🔴 **Красный** - отклонения (повышен/понижен/аномалия)
  - 🔵 **Синий** - прочие статусы

### 📱 Навигация

#### Открытие экрана:
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => LabReportDetailScreen(
      reportId: report.id,
    ),
  ),
);
```

#### Возврат:
- Кнопка "назад" в AppBar
- Системная кнопка "назад"

### 📁 Файлы:
- `lib/screens/lab_report_detail_screen.dart` - основной экран
- `test/lab_report_detail_screen_test.dart` - тесты экрана

### 🔧 Используемые сервисы:
- `LabReportService.getLabReportDetail(reportId)` - получение детальной информации

### 📋 Модели данных:
- `LabReportDetail` - детальная информация об отчете с анализами
- `LabReport` - информация об отчете
- `LabTest` - данные отдельного анализа

## Пример отображения

```
════════════════════════════════════════
← Результаты                          ✏️
════════════════════════════════════════

👤 Профиль                         Alex
   Дата                    28 янв 2025
   Лаборатория         Инвитро AstraLab
   Заметки                           >

┌─────────────────────────────────────┐
│    Результаты    │    Документ     │
└─────────────────────────────────────┘

Биомаркеры

┌─────────────────────────────────────┐
│ Хеликобактер антиген, качественно  🟢│
│ Не обнаружено                       │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ Глюкоза                            🔴│
│ Повышен                             │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ Холестерин общий                   🔴│
│ Повышен                             │
└─────────────────────────────────────┘
```

## Состояния экрана

### 🔄 Загрузка:
- Показывается CircularProgressIndicator
- AppBar остается видимым

### ❌ Ошибка:
- Иконка ошибки и сообщение
- Кнопка "Повторить" для повторной загрузки

### ✅ Успешная загрузка:
- Полная информация об отчете
- Список всех анализов
- Интерактивные табы

## Тестирование

### Запуск тестов:
```bash
flutter test test/lab_report_detail_screen_test.dart
```

### Покрытие тестами:
- ✅ Создание экрана без ошибок
- ✅ Отображение индикатора загрузки
- ✅ Корректность AppBar (заголовок, кнопки)
- ✅ Интерактивность табов
- ✅ Маппинг статусов анализов
- ✅ Навигация (кнопка назад)
- ✅ Передача reportId
- ✅ Обработка ошибок

### Результат: 9/9 тестов проходят ✅

## Тестирование

### Запуск тестов:
```bash
flutter test test/lab_reports_screen_test.dart
```

### Демонстрация:
```dart
// Показать экран с тестовыми данными
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const LabReportsDemoScreen(),
  ),
);
```

## Состояния экрана

1. **Загрузка** - отображается `CircularProgressIndicator`
2. **Успешная загрузка** - показываются данные анализов
3. **Ошибка** - отображается сообщение об ошибке с кнопкой повтора
4. **Нет данных** - сообщение о том, что анализы не найдены

## Обработка ошибок

- **Нет подключения к API** - "Нет подключения к интернету"
- **Пользователь не найден** - "Нет данных анализов для данного пользователя"
- **Ошибка сервера** - отображается конкретное сообщение об ошибке
- **Неизвестная ошибка** - общее сообщение с возможностью повтора

Экран полностью готов к использованию и предоставляет удобный интерфейс для просмотра медицинских анализов!
