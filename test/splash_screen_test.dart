import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/screens/splash_screen.dart';

void main() {
  group('SplashScreen Tests', () {
    // Примечание: SplashScreen содержит анимации и таймеры,
    // поэтому мы тестируем только создание объекта

    test('SplashScreen should be created without parameters', () {
      const screen = SplashScreen();
      expect(screen, isA<SplashScreen>());
    });

    test('SplashScreen should be a StatefulWidget', () {
      const screen = SplashScreen();
      expect(screen, isA<StatefulWidget>());
    });
  });
}
