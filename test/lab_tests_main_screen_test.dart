import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/screens/lab_tests_main_screen.dart';
import 'package:medstata_app/screens/lab_reports_screen.dart';

void main() {
  group('LabTestsMainScreen Widget Tests', () {
    testWidgets('should create LabTestsMainScreen without crashing', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Проверяем, что экран создается без ошибок
      expect(find.byType(LabTestsMainScreen), findsOneWidget);
      expect(find.byType(Container), findsWidgets);
    });

    testWidgets('should display tab bar with Биомаркеры and Документы', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Проверяем наличие табов
      expect(find.text('Биомаркеры'), findsOneWidget);
      expect(find.text('Документы'), findsOneWidget);
    });

    testWidgets('should show loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Проверяем наличие индикатора загрузки
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Загрузка анализов...'), findsOneWidget);
    });

    testWidgets('should have search and filter buttons in header', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Проверяем наличие кнопок в заголовке
      expect(find.byIcon(Icons.search), findsOneWidget);
      expect(find.byIcon(Icons.more_horiz), findsOneWidget);
    });

    testWidgets('should switch between tabs', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Изначально выбран первый таб (Биомаркеры)
      expect(find.text('Биомаркеры'), findsOneWidget);

      // Нажимаем на таб "Документы"
      await tester.tap(find.text('Документы'));
      await tester.pumpAndSettle();

      // Проверяем, что таб переключился - ищем LabReportsScreen
      expect(find.byType(LabReportsScreen), findsOneWidget);
    });

    testWidgets('should show period selector for Биомаркеры tab', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Проверяем наличие селектора периода для первого таба
      expect(find.text('За последний год'), findsOneWidget);
      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    testWidgets('should not show period selector for Документы tab', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Переключаемся на таб "Документы"
      await tester.tap(find.text('Документы'));
      await tester.pumpAndSettle();

      // Проверяем, что селектор периода не отображается
      expect(find.text('За последний год'), findsNothing);
    });

    testWidgets('should have correct background color', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsMainScreen(),
        ),
      );

      // Проверяем, что контейнер имеет правильный цвет фона
      final container = tester.widget<Container>(find.byType(Container).first);
      expect(container.color, equals(Colors.grey.shade50));
    });
  });
}
