import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/models/lab_report.dart';

void main() {
  group('LabReports Sorting Tests', () {
    late List<LabReport> testReports;

    setUp(() {
      testReports = [
        LabReport(
          id: 'report1',
          analysisName: 'Биохимические исследования крови',
          performedAt: '2025-01-15',
          createdAt: '2025-01-20T10:00:00Z',
          status: LabReportStatus.completed,
          testsCount: 5,
        ),
        LabReport(
          id: 'report2',
          analysisName: 'Общий анализ крови',
          performedAt: '2025-01-10',
          createdAt: '2025-01-18T14:30:00Z',
          status: LabReportStatus.processing,
          testsCount: 3,
        ),
        LabReport(
          id: 'report3',
          analysisName: 'Анализ мочи',
          performedAt: '2025-01-20',
          createdAt: '2025-01-15T09:15:00Z',
          status: LabReportStatus.pending,
          testsCount: 2,
        ),
      ];
    });

    test('should sort by createdAt (По дате добавления) correctly', () {
      // Сортируем по дате добавления (новые сначала)
      testReports.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      expect(testReports[0].id, equals('report1')); // 2025-01-20
      expect(testReports[1].id, equals('report2')); // 2025-01-18
      expect(testReports[2].id, equals('report3')); // 2025-01-15
    });

    test('should sort by performedAt (По дате выполнения) correctly', () {
      // Сортируем по дате выполнения (новые сначала)
      testReports.sort((a, b) => b.performedAt.compareTo(a.performedAt));

      expect(testReports[0].id, equals('report3')); // 2025-01-20
      expect(testReports[1].id, equals('report1')); // 2025-01-15
      expect(testReports[2].id, equals('report2')); // 2025-01-10
    });

    test('should sort by analysisName (По названию) correctly', () {
      // Сортируем по названию (алфавитный порядок)
      testReports.sort((a, b) => (a.analysisName ?? '').compareTo(b.analysisName ?? ''));

      expect(testReports[0].analysisName, equals('Анализ мочи'));
      expect(testReports[1].analysisName, equals('Биохимические исследования крови'));
      expect(testReports[2].analysisName, equals('Общий анализ крови'));
    });

    test('should sort by status (По статусу) correctly', () {
      // Сортируем по статусу
      testReports.sort((a, b) => a.status.value.compareTo(b.status.value));

      // Проверяем, что сортировка работает по значению статуса
      expect(testReports.length, equals(3));
      // Точный порядок зависит от значений enum, но важно что сортировка работает
      expect(testReports.every((report) => report.status != null), isTrue);
    });

    test('LabReport createdAt formatting should work correctly', () {
      final report = LabReport(
        id: 'test',
        performedAt: '2025-01-15',
        createdAt: '2025-01-20T14:30:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      expect(report.formattedCreatedDate, equals('20 янв 2025'));
    });

    test('LabReport should handle different date formats for createdAt', () {
      final report1 = LabReport(
        id: 'test1',
        performedAt: '2025-01-15',
        createdAt: '2025-03-28T14:30:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      final report2 = LabReport(
        id: 'test2',
        performedAt: '2025-01-15',
        createdAt: '2025-12-05T09:15:30Z',
        status: LabReportStatus.completed,
        testsCount: 3,
      );

      expect(report1.formattedCreatedDate, equals('28 мар 2025'));
      expect(report2.formattedCreatedDate, equals('5 дек 2025'));
    });

    test('LabReport should handle invalid createdAt gracefully', () {
      final report = LabReport(
        id: 'test',
        performedAt: '2025-01-15',
        createdAt: 'invalid-date',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      // Должен вернуть исходную строку при ошибке парсинга
      expect(report.formattedCreatedDate, equals('invalid-date'));
    });
  });
}
