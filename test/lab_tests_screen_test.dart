import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/screens/lab_tests_screen.dart';

void main() {
  group('LabTestsScreen Widget Tests', () {
    testWidgets('should create LabTestsScreen without crashing', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LabTestsScreen(),
        ),
      );

      // Проверяем, что экран создается без ошибок
      expect(find.byType(LabTestsScreen), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });
  });
}
