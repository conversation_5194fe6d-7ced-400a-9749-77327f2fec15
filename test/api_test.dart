import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/medstata_api.dart';

void main() {
  group('MedStata API Tests', () {
    late PatientService patientService;
    late LabTestService labTestService;

    setUp(() {
      // Используем реальный API для тестов
      patientService = PatientService();
      labTestService = LabTestService();
    });

    tearDown(() {
      patientService.dispose();
      labTestService.dispose();
    });

    group('Patient Service Tests', () {
      test('should get patient data successfully (requires auth)', () async {
        // Примечание: Этот тест требует авторизации
        // В реальном приложении нужно сначала авторизоваться
        try {
          final response = await patientService.getPatient();

          if (response.isSuccess) {
            expect(response.status, equals('ok'));
            expect(response.data, isNotNull);
            expect(response.data!.age, isA<int>());
            expect(response.data!.gender, isA<Gender>());
          } else {
            // Ожидаем ошибку авторизации без токена
            expect(response.isSuccess, isFalse);
          }
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e.toString(), contains('авторизация'));
        }
      });

      test('should handle unauthorized access', () async {
        try {
          final response = await patientService.getPatient();
          // Если запрос прошел, должен быть статус ошибки
          expect(response.isSuccess, isFalse);
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e, isA<Exception>());
        }
      });

      test('should check if patient exists (requires auth)', () async {
        try {
          final exists = await patientService.patientExists();
          expect(exists, isA<bool>());
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e, isA<Exception>());
        }
      });
    });

    group('Lab Test Service Tests', () {
      test('should get lab tests successfully (requires auth)', () async {
        try {
          final response = await labTestService.getLabTests();

          if (response.isSuccess) {
            expect(response.data, isNotNull);
            expect(response.data!.totalCount, isA<int>());
            expect(response.data!.normalCount, isA<int>());
            expect(response.data!.abnormalCount, isA<int>());
            expect(response.data!.otherCount, isA<int>());
            expect(response.data!.groupLabTests, isA<Map<String, List<LabTest>>>());
          } else {
            // Может быть no_data или ошибка авторизации
            expect(response.isNoData || !response.isSuccess, isTrue);
          }
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e, isA<Exception>());
        }
      });

      test('should get lab test statistics (requires auth)', () async {
        try {
          final stats = await labTestService.getLabTestsStatistics();

          if (stats != null) {
            expect(stats['total'], isA<int>());
            expect(stats['normal'], isA<int>());
            expect(stats['abnormal'], isA<int>());
            expect(stats['other'], isA<int>());
          }
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e, isA<Exception>());
        }
      });

      test('should get lab test categories (requires auth)', () async {
        try {
          final categories = await labTestService.getLabTestCategories();

          if (categories != null) {
            expect(categories, isA<List<String>>());
          }
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e, isA<Exception>());
        }
      });

      test('should check if user has lab tests (requires auth)', () async {
        try {
          final hasTests = await labTestService.hasLabTests();
          expect(hasTests, isA<bool>());
        } catch (e) {
          // Ожидаем исключение без авторизации
          expect(e, isA<Exception>());
        }
      });
    });

    group('Model Tests', () {
      test('should create Patient from JSON', () {
        final json = {
          'age': 36,
          'gender': 'MALE',
        };

        final patient = Patient.fromJson(json);

        expect(patient.age, equals(36));
        expect(patient.gender, equals(Gender.male));
      });

      test('should create LabTest from JSON', () {
        final json = {
          'id': 'test123',
          'loincCode': '2345-7',
          'name': 'Глюкоза',
          'status': 'NORMAL',
          'interpretationReasons': <String>[],
          'performedAt': '2025-01-15',
          'value': {
            'value': '5.2',
            'referenceRange': '3.9-6.1',
            'unitLabel': 'ммоль/л',
          },
        };

        final labTest = LabTest.fromJson(json);

        expect(labTest.id, equals('test123'));
        expect(labTest.name, equals('Глюкоза'));
        expect(labTest.status, equals(LabTestStatus.normal));
        expect(labTest.value.value, equals('5.2'));
        expect(labTest.value.unitLabel, equals('ммоль/л'));
      });

      test('should handle enum conversion correctly', () {
        expect(Gender.fromString('MALE'), equals(Gender.male));
        expect(Gender.fromString('FEMALE'), equals(Gender.female));
        expect(Gender.fromString('UNKNOWN'), equals(Gender.other));

        // Тестируем различные варианты статусов (верхний и нижний регистр)
        expect(LabTestStatus.fromString('NORMAL'), equals(LabTestStatus.normal));
        expect(LabTestStatus.fromString('normal'), equals(LabTestStatus.normal));
        expect(LabTestStatus.fromString('ELEVATED'), equals(LabTestStatus.elevated));
        expect(LabTestStatus.fromString('elevated'), equals(LabTestStatus.elevated));
        expect(LabTestStatus.fromString('LOW'), equals(LabTestStatus.low));
        expect(LabTestStatus.fromString('low'), equals(LabTestStatus.low));
        expect(LabTestStatus.fromString('ELEVATED'), equals(LabTestStatus.elevated));
        expect(LabTestStatus.fromString('elevated'), equals(LabTestStatus.elevated));

        // Тестируем null и неизвестные значения
        expect(LabTestStatus.fromString(null), equals(LabTestStatus.other));
        expect(LabTestStatus.fromString(''), equals(LabTestStatus.other));
        expect(LabTestStatus.fromString('UNKNOWN'), equals(LabTestStatus.other));
      });
    });

    group('API Response Tests', () {
      test('should create successful API response', () {
        final json = {
          'status': 'ok',
          'data': {'age': 36, 'gender': 'MALE'},
        };

        final response = ApiResponse.fromJson(
          json,
          (data) => Patient.fromJson(data as Map<String, dynamic>),
        );

        expect(response.isSuccess, isTrue);
        expect(response.data, isNotNull);
        expect(response.data!.age, equals(36));
      });

      test('should create error API response', () {
        final json = {
          'status': 'user_not_found',
          'message': 'User not found',
        };

        final response = ApiResponse<Patient>.fromJson(json, null);

        expect(response.isUserNotFound, isTrue);
        expect(response.message, equals('User not found'));
        expect(response.data, isNull);
      });
    });
  });
}
