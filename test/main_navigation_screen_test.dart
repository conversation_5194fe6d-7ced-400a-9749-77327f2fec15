import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/screens/main_navigation_screen.dart';

void main() {
  group('MainNavigationScreen Widget Tests', () {
    testWidgets('should create MainNavigationScreen without crashing', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: MainNavigationScreen(),
        ),
      );

      // Проверяем, что экран создается без ошибок
      expect(find.byType(MainNavigationScreen), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('should have correct AppBar title for default tab', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: MainNavigationScreen(),
        ),
      );

      // Проверяем заголовок по умолчанию (первый таб - "Анализы")
      // Ищем текст в AppBar
      expect(find.descendant(
        of: find.byType(AppBar),
        matching: find.text('Анализы'),
      ), findsOneWidget);
    });

    testWidgets('should have bottom navigation with 3 items', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: MainNavigationScreen(),
        ),
      );

      // Проверяем наличие элементов навигации
      expect(find.text('Анализы'), findsWidgets); // В AppBar и в навигации
      expect(find.text('Загрузить'), findsOneWidget); // Кнопка загрузки
      expect(find.text('Настройки'), findsOneWidget);
    });

    testWidgets('should change AppBar title when switching tabs', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: MainNavigationScreen(),
        ),
      );

      // Изначально должен быть заголовок "Анализы"
      expect(find.descendant(
        of: find.byType(AppBar),
        matching: find.text('Анализы'),
      ), findsOneWidget);

      // Нажимаем на таб "Настройки"
      await tester.tap(find.text('Настройки'));
      await tester.pumpAndSettle();

      // Проверяем, что заголовок изменился
      expect(find.descendant(
        of: find.byType(AppBar),
        matching: find.text('Настройки'),
      ), findsOneWidget);
    });

    testWidgets('should have IndexedStack for preserving state', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: MainNavigationScreen(),
        ),
      );

      // Проверяем наличие IndexedStack в MainNavigationScreen
      expect(find.descendant(
        of: find.byType(MainNavigationScreen),
        matching: find.byType(IndexedStack),
      ), findsAtLeastNWidgets(1));
    });

    testWidgets('should have upload button with special styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: MainNavigationScreen(),
        ),
      );

      // Проверяем наличие кнопки загрузки
      expect(find.text('Загрузить'), findsOneWidget);

      // Проверяем наличие иконки добавления
      expect(find.byIcon(Icons.add_outlined), findsOneWidget);
    });
  });
}
