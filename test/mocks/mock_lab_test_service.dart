import 'package:medstata_app/medstata_api.dart';

/// Мок-сервис для LabTestService для использования в тестах
class MockLabTestService extends LabTestService {
  GroupedLabTests? _mockResponse;
  bool _shouldReturnError = false;
  bool _shouldReturnNoData = false;

  /// Устанавливает тестовые данные для ответа
  void setMockResponse(GroupedLabTests response) {
    _mockResponse = response;
  }

  /// Заставляет сервис возвращать ошибку
  void setShouldReturnError(bool shouldError) {
    _shouldReturnError = shouldError;
  }

  /// Заставляет сервис возвращать no_data
  void setShouldReturnNoData(bool shouldReturnNoData) {
    _shouldReturnNoData = shouldReturnNoData;
  }

  @override
  Future<ApiResponse<GroupedLabTests>> getLabTests() async {
    // Симулируем задержку сети
    await Future.delayed(const Duration(milliseconds: 100));

    if (_shouldReturnError) {
      return ApiResponse<GroupedLabTests>.error('Тестовая ошибка');
    }

    if (_shouldReturnNoData || _mockResponse == null) {
      return ApiResponse<GroupedLabTests>.noData('Нет данных');
    }

    return ApiResponse<GroupedLabTests>.success(_mockResponse!);
  }

  /// Создает тестовые данные для GroupedLabTests
  static GroupedLabTests createTestResponse() {
    final tests = [
      LabTest(
        id: 'test_1',
        name: 'Глюкоза',
        status: LabTestStatus.normal,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '5.2', unitLabel: 'ммоль/л'),
      ),
      LabTest(
        id: 'test_2',
        name: 'Холестерин общий',
        status: LabTestStatus.elevated,
        interpretationReasons: ['Повышен'],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '6.8', unitLabel: 'ммоль/л'),
      ),
      LabTest(
        id: 'test_3',
        name: 'Гемоглобин',
        status: LabTestStatus.low,
        interpretationReasons: ['Понижен'],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '110', unitLabel: 'г/л'),
      ),
    ];

    // Группируем тесты по категориям
    final groupLabTests = <String, List<LabTest>>{
      'Биохимические исследования': [tests[0], tests[1]],
      'Общий анализ крови': [tests[2]],
    };

    return GroupedLabTests(
      totalCount: tests.length,
      normalCount: 1,
      abnormalCount: 2,
      otherCount: 0,
      groupLabTests: groupLabTests,
    );
  }
}
