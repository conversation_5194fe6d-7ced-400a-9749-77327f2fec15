import 'package:medstata_app/medstata_api.dart';

/// Мок-сервис для LabReportService для использования в тестах
class MockLabReportService extends LabReportService {
  List<LabReport> _mockReports = [];
  Map<String, LabReportDetail> _mockDetails = {};
  bool _shouldReturnError = false;
  bool _shouldReturnNoData = false;

  /// Устанавливает тестовые данные для отчетов
  void setMockReports(List<LabReport> reports) {
    _mockReports = reports;
  }

  /// Устанавливает тестовые данные для деталей отчета
  void setMockReportDetail(String reportId, LabReportDetail detail) {
    _mockDetails[reportId] = detail;
  }

  /// Заставляет сервис возвращать ошибку
  void setShouldReturnError(bool shouldError) {
    _shouldReturnError = shouldError;
  }

  /// Заставляет сервис возвращать no_data
  void setShouldReturnNoData(bool shouldReturnNoData) {
    _shouldReturnNoData = shouldReturnNoData;
  }

  @override
  Future<ApiResponse<List<LabReport>>> getLabReports() async {
    // Симулируем задержку сети
    await Future.delayed(const Duration(milliseconds: 100));

    if (_shouldReturnError) {
      return ApiResponse<List<LabReport>>.error('Тестовая ошибка');
    }

    if (_shouldReturnNoData || _mockReports.isEmpty) {
      return ApiResponse<List<LabReport>>.noData('Нет данных');
    }

    return ApiResponse<List<LabReport>>.success(_mockReports);
  }

  @override
  Future<ApiResponse<LabReportDetail>> getLabReportDetail(String reportId) async {
    // Симулируем задержку сети
    await Future.delayed(const Duration(milliseconds: 100));

    if (_shouldReturnError) {
      return ApiResponse<LabReportDetail>.error('Тестовая ошибка');
    }

    final detail = _mockDetails[reportId];
    if (detail == null) {
      return ApiResponse<LabReportDetail>.noData('Отчет не найден');
    }

    return ApiResponse<LabReportDetail>.success(detail);
  }

  /// Создает тестовые данные для отчетов
  static List<LabReport> createTestReports() {
    return [
      LabReport(
        id: 'test_report_1',
        analysisName: 'Биохимические исследования крови',
        performedAt: '2025-01-15',
        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
        laboratoryName: 'Тестовая лаборатория',
        doctorName: 'Иванов И.И.',
        clinicName: 'Тестовая клиника',
      ),
      LabReport(
        id: 'test_report_2',
        analysisName: 'Общий анализ крови',
        performedAt: '2025-01-10',
        createdAt: '2025-01-08T14:30:00Z',
        status: LabReportStatus.processing,
        testsCount: 3,
        laboratoryName: 'Другая лаборатория',
      ),
      LabReport(
        id: 'test_report_3',
        analysisName: 'Анализ мочи',
        performedAt: '2025-01-20',
        createdAt: '2025-01-18T09:15:00Z',
        status: LabReportStatus.pending,
        testsCount: 2,
      ),
    ];
  }

  /// Создает тестовые данные для деталей отчета
  static LabReportDetail createTestReportDetail(String reportId) {
    final report = LabReport(
      id: reportId,
      analysisName: 'Тестовый анализ',
      performedAt: '2025-01-15',
      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.completed,
      testsCount: 2,
      laboratoryName: 'Тестовая лаборатория',
    );

    final tests = [
      LabTest(
        id: 'test_1',
        name: 'Глюкоза',
        status: LabTestStatus.normal,
        interpretationReasons: [],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '5.2', unitLabel: 'ммоль/л'),
      ),
      LabTest(
        id: 'test_2',
        name: 'Холестерин',
        status: LabTestStatus.elevated,
        interpretationReasons: ['Повышен'],
        performedAt: '2025-01-15',
        value: LabTestValue(value: '6.8', unitLabel: 'ммоль/л'),
      ),
    ];

    return LabReportDetail(
      report: report,
      tests: tests,
      patientAge: '30 лет',
      patientGender: 'MALE',
    );
  }
}
