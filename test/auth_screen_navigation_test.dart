import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/services/auth_state_service.dart';
import 'package:medstata_app/services/storage_service.dart';

void main() {
  group('AuthScreen Navigation Logic Tests', () {
    late AuthStateService authStateService;

    setUp(() async {
      // Инициализируем StorageService для тестов
      await StorageService.init();

      // Очищаем состояние авторизации перед каждым тестом
      authStateService = AuthStateService();
      authStateService.setAuthenticationStatus(false);

      // Очищаем сохраненные токены
      StorageService.clearAuthTokens();
    });

    test('AuthStateService should notify listeners when authentication status changes', () {
      bool listenerCalled = false;
      bool authStatus = false;

      // Подписываемся на изменения
      void listener() {
        listenerCalled = true;
        authStatus = authStateService.isAuthenticated;
      }

      authStateService.addListener(listener);

      // Проверяем начальное состояние
      expect(authStateService.isAuthenticated, isFalse);
      expect(listenerCalled, isFalse);

      // Симулируем успешную авторизацию
      authStateService.setAuthenticationStatus(true);

      // Проверяем, что слушатель был вызван
      expect(listenerCalled, isTrue);
      expect(authStatus, isTrue);
      expect(authStateService.isAuthenticated, isTrue);

      // Очищаем слушатель
      authStateService.removeListener(listener);
    });

    test('AuthStateService should handle multiple state changes correctly', () {
      int listenerCallCount = 0;
      List<bool> authStates = [];

      // Подписываемся на изменения
      void listener() {
        listenerCallCount++;
        authStates.add(authStateService.isAuthenticated);
      }

      authStateService.addListener(listener);

      // Проверяем начальное состояние
      expect(authStateService.isAuthenticated, isFalse);
      expect(listenerCallCount, 0);

      // Первое изменение: false -> true
      authStateService.setAuthenticationStatus(true);
      expect(listenerCallCount, 1);
      expect(authStates.last, isTrue);

      // Второе изменение: true -> false
      authStateService.setAuthenticationStatus(false);
      expect(listenerCallCount, 2);
      expect(authStates.last, isFalse);

      // Попытка установить то же состояние - слушатель не должен вызываться
      authStateService.setAuthenticationStatus(false);
      expect(listenerCallCount, 2); // Не изменилось

      // Третье изменение: false -> true
      authStateService.setAuthenticationStatus(true);
      expect(listenerCallCount, 3);
      expect(authStates.last, isTrue);

      // Проверяем последовательность состояний
      expect(authStates, equals([true, false, true]));

      // Очищаем слушатель
      authStateService.removeListener(listener);
    });

    test('AuthStateService login and logout methods should work correctly', () {
      bool listenerCalled = false;
      bool finalAuthState = false;

      // Подписываемся на изменения
      void listener() {
        listenerCalled = true;
        finalAuthState = authStateService.isAuthenticated;
      }

      authStateService.addListener(listener);

      // Тестируем метод login()
      authStateService.login();
      expect(listenerCalled, isTrue);
      expect(finalAuthState, isTrue);
      expect(authStateService.isAuthenticated, isTrue);

      // Сбрасываем флаги
      listenerCalled = false;

      // Тестируем метод logout()
      authStateService.logout();
      expect(listenerCalled, isTrue);
      expect(finalAuthState, isFalse);
      expect(authStateService.isAuthenticated, isFalse);

      // Очищаем слушатель
      authStateService.removeListener(listener);
    });
  });
}
