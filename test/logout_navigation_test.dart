import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/services/auth_service.dart';
import 'package:medstata_app/services/auth_state_service.dart';

void main() {
  group('Logout Navigation Tests', () {
    late AuthService authService;
    late AuthStateService authStateService;

    setUp(() {
      authService = AuthService();
      authStateService = AuthStateService();
    });

    test('AuthService logout should notify AuthStateService', () {
      // Устанавливаем начальное состояние как авторизованный
      authStateService.setAuthenticationStatus(true);
      expect(authStateService.isAuthenticated, isTrue);

      // Выполняем logout через AuthService
      authService.logout();

      // Проверяем, что AuthStateService получил уведомление
      expect(authStateService.isAuthenticated, isFalse);
    });

    test('AuthStateService logout should change authentication status', () {
      // Устанавливаем начальное состояние как авторизованный
      authStateService.setAuthenticationStatus(true);
      expect(authStateService.isAuthenticated, isTrue);

      // Выполняем logout через AuthStateService
      authStateService.logout();

      // Проверяем, что статус изменился
      expect(authStateService.isAuthenticated, isFalse);
    });

    test('AuthStateService should notify listeners on logout', () {
      bool listenerCalled = false;
      bool newAuthState = true;

      // Подписываемся на изменения
      void listener() {
        listenerCalled = true;
        newAuthState = authStateService.isAuthenticated;
      }

      authStateService.addListener(listener);

      // Устанавливаем начальное состояние как авторизованный
      authStateService.setAuthenticationStatus(true);
      listenerCalled = false; // Сбрасываем флаг

      // Выполняем logout
      authStateService.logout();

      // Проверяем, что слушатель был вызван
      expect(listenerCalled, isTrue);
      expect(newAuthState, isFalse);

      // Очищаем слушатель
      authStateService.removeListener(listener);
    });

    test('Multiple logout calls should not cause issues', () {
      // Устанавливаем начальное состояние как авторизованный
      authStateService.setAuthenticationStatus(true);
      expect(authStateService.isAuthenticated, isTrue);

      // Выполняем несколько logout подряд
      authService.logout();
      authService.logout();
      authStateService.logout();

      // Проверяем, что статус остается false
      expect(authStateService.isAuthenticated, isFalse);
    });

    test('AuthService properties should be cleared after logout', () {
      // Симулируем авторизованное состояние с длинными токенами
      authService.setTokens(
        accessToken: 'test_access_token_that_is_long_enough_for_substring_operations',
        refreshToken: 'test_refresh_token_that_is_long_enough_for_substring_operations',
        userId: 'test_user_id',
      );

      // Проверяем, что токены установлены
      expect(authService.accessToken, isNotNull);
      expect(authService.refreshToken, isNotNull);
      expect(authService.userId, isNotNull);
      expect(authService.isAuthenticated, isTrue);

      // Выполняем logout
      authService.logout();

      // Проверяем, что все токены очищены
      expect(authService.accessToken, isNull);
      expect(authService.refreshToken, isNull);
      expect(authService.userId, isNull);
      expect(authService.isAuthenticated, isFalse);
    });
  });
}
