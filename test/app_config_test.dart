import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/config/app_config.dart';

void main() {
  group('AppConfig', () {
    tearDown(() {
      AppConfig.resetEnvironmentOverride();
    });

    test('DEV environment values are correct', () {
      AppConfig.overrideEnvironment('DEV');

      expect(AppConfig.isProd, false);
      expect(AppConfig.baseUrl, 'https://medstata.avpuser.ru');
      expect(AppConfig.telegramAuthPage, 'https://medstata.avpuser.ru/telegram-auth-complete.html');
      expect(AppConfig.telegramOAuthUrl, contains('bot_id=8134568637'));
      expect(AppConfig.telegramOAuthUrl, contains('origin=https://medstata.avpuser.ru'));
      expect(AppConfig.telegramOAuthUrl, contains('return_to=https://medstata.avpuser.ru/telegram-auth-complete.html'));
    });

    test('PROD environment values are correct', () {
      AppConfig.overrideEnvironment('PROD');

      expect(AppConfig.isProd, true);
      expect(AppConfig.baseUrl, 'https://api.medstata.com');
      expect(AppConfig.telegramAuthPage, 'https://api.medstata.com/telegram-auth-complete.html');
      expect(AppConfig.telegramOAuthUrl, contains('bot_id=8044412847'));
      expect(AppConfig.telegramOAuthUrl, contains('origin=https://api.medstata.com'));
      expect(AppConfig.telegramOAuthUrl, contains('return_to=https://api.medstata.com/telegram-auth-complete.html'));
    });
  });
}