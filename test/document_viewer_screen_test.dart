import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/screens/document_viewer_screen.dart';

void main() {
  group('DocumentViewerScreen Tests', () {
    // Примечание: WebView тесты требуют платформенную реализацию
    // Поэтому мы тестируем только создание объекта

    test('DocumentViewerScreen should be created with required parameters', () {
      const testUrl = 'https://example.com/document.pdf';
      const testName = 'Test Document.pdf';

      final screen = DocumentViewerScreen(
        documentUrl: testUrl,
        documentName: testName,
      );

      expect(screen.documentUrl, equals(testUrl));
      expect(screen.documentName, equals(testName));
    });

    test('DocumentViewerScreen should handle empty document name', () {
      const testUrl = 'https://example.com/document.pdf';
      const emptyName = '';

      final screen = DocumentViewerScreen(
        documentUrl: testUrl,
        documentName: emptyName,
      );

      expect(screen.documentUrl, equals(testUrl));
      expect(screen.documentName, equals(emptyName));
    });
  });
}
