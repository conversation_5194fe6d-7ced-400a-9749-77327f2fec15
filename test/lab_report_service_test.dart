import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/medstata_api.dart';

void main() {
  group('LabReportService Tests', () {
    late LabReportService labReportService;

    setUp(() {
      labReportService = LabReportService();
    });

    tearDown(() {
      labReportService.dispose();
    });

    test('LabReport model should parse JSON correctly', () {
      final json = {
        'id': 'report_123',
        'analysisName': 'Биохимические исследования крови',
        'performedAt': '2025-01-15',
        'specimenMaterial': 'сыворотка крови',
        'reportStatus': 'DONE',
        'testCount': 12,
        'laboratory': {'name': 'Инвитро AstraLab'},
        'doctor': {'name': 'Альтшулер Б. Ю.'},
        'clinic': {'name': 'КДЛ'}
      };

      final report = LabReport.fromJson(json);

      expect(report.id, equals('report_123'));
      expect(report.analysisName, equals('Биохимические исследования крови'));
      expect(report.performedAt, equals('2025-01-15'));
      expect(report.specimenMaterial, equals('сыворотка крови'));
      expect(report.status, equals(LabReportStatus.completed));
      expect(report.testsCount, equals(12));
      expect(report.laboratoryName, equals('Инвитро AstraLab'));
      expect(report.doctorName, equals('Альтшулер Б. Ю.'));
      expect(report.clinicName, equals('КДЛ'));
      expect(report.isCompleted, isTrue);
      expect(report.isProcessing, isFalse);
      expect(report.isFailed, isFalse);
    });

    test('LabReportStatus should parse correctly', () {
      expect(LabReportStatus.fromString('PENDING'), equals(LabReportStatus.pending));
      expect(LabReportStatus.fromString('PROCESSING'), equals(LabReportStatus.processing));
      expect(LabReportStatus.fromString('COMPLETED'), equals(LabReportStatus.completed));
      expect(LabReportStatus.fromString('FAILED'), equals(LabReportStatus.failed));
      expect(LabReportStatus.fromString('UNKNOWN'), equals(LabReportStatus.pending));
      expect(LabReportStatus.fromString(null), equals(LabReportStatus.pending));

      // Тестируем маппинг для реального API через fromJson
      final doneReport = LabReport.fromJson({'id': 'test', 'performedAt': '2025-01-15', 'reportStatus': 'DONE', 'testCount': 1});
      final errorReport = LabReport.fromJson({'id': 'test', 'performedAt': '2025-01-15', 'reportStatus': 'ERROR', 'testCount': 1});

      expect(doneReport.status, equals(LabReportStatus.completed));
      expect(errorReport.status, equals(LabReportStatus.failed));
    });

    test('LabReportStatus display names should be correct', () {
      expect(LabReportStatus.pending.displayName, equals('Ожидает обработки'));
      expect(LabReportStatus.processing.displayName, equals('Обрабатывается'));
      expect(LabReportStatus.completed.displayName, equals('Обработан'));
      expect(LabReportStatus.failed.displayName, equals('Ошибка обработки'));
    });

    test('LabReport formatted date should work correctly', () {
      final report = LabReport(
        id: 'test',
        performedAt: '2025-01-15',
        createdAt: '2025-01-10T12:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      expect(report.formattedPerformedDate, equals('15 янв 2025'));
      expect(report.formattedFullDate, equals('15.01.2025'));
      expect(report.formattedCreatedDate, equals('10 янв 2025'));
    });

    test('LabReport JSON serialization should work correctly', () {
      final report = LabReport(
        id: 'test1',
        analysisName: 'Биохимические исследования крови',
        performedAt: '2025-01-15',

        createdAt: '2025-01-10T10:00:00Z',
        specimenMaterial: 'сыворотка крови',
        status: LabReportStatus.completed,
        testsCount: 5,
        laboratoryName: 'Инвитро AstraLab',
        doctorName: 'Альтшулер Б. Ю.',
        clinicName: 'КДЛ',
      );

      final json = report.toJson();

      expect(json['id'], equals('test1'));
      expect(json['analysisName'], equals('Биохимические исследования крови'));
      expect(json['performedAt'], equals('2025-01-15'));
      expect(json['specimenMaterial'], equals('сыворотка крови'));
      expect(json['reportStatus'], equals('COMPLETED'));
      expect(json['testCount'], equals(5));
      expect(json['laboratory']['name'], equals('Инвитро AstraLab'));
      expect(json['doctor']['name'], equals('Альтшулер Б. Ю.'));
      expect(json['clinic']['name'], equals('КДЛ'));
    });

    test('LabReport equality should work correctly', () {
      final report1 = LabReport(
        id: 'test',
        performedAt: '2025-01-15',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      final report2 = LabReport(
        id: 'test',
        analysisName: 'Different analysis',
        performedAt: '2025-01-16',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.pending,
        testsCount: 10,
      );

      final report3 = LabReport(
        id: 'different',
        performedAt: '2025-01-15',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
      );

      expect(report1, equals(report2)); // Same ID
      expect(report1, isNot(equals(report3))); // Different ID
      expect(report1.hashCode, equals(report2.hashCode));
      expect(report1.hashCode, isNot(equals(report3.hashCode)));
    });

    test('getTotalTestsCount should work with mock data', () async {
      // Создаем тестовые отчеты
      final reports = [
        LabReport(
          id: 'report1',
          performedAt: '2025-01-15',

          createdAt: '2025-01-10T10:00:00Z',
          status: LabReportStatus.completed,
          testsCount: 5,
        ),
        LabReport(
          id: 'report2',
          performedAt: '2025-01-16',

          createdAt: '2025-01-10T10:00:00Z',
          status: LabReportStatus.completed,
          testsCount: 10,
        ),
        LabReport(
          id: 'report3',
          performedAt: '2025-01-17',

          createdAt: '2025-01-10T10:00:00Z',
          status: LabReportStatus.processing,
          testsCount: 3, // Этот не должен учитываться, так как не завершен
        ),
      ];

      // Тестируем логику подсчета
      int total = 0;
      for (final report in reports) {
        if (report.status == LabReportStatus.completed) {
          total += report.testsCount;
        }
      }

      expect(total, equals(15)); // 5 + 10 = 15 (только завершенные)
    });

    test('LabReportDetail model should work correctly', () {
      final report = LabReport(
        id: 'report_123',
        analysisName: 'Биохимические исследования крови',
        performedAt: '2025-01-15',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 3,
      );

      final tests = [
        LabTest(
          id: 'test_1',
          name: 'Глюкоза',
          status: LabTestStatus.normal,
          interpretationReasons: [],
          performedAt: '2025-01-15',
          value: LabTestValue(value: '5.2', unitLabel: 'ммоль/л'),
        ),
        LabTest(
          id: 'test_2',
          name: 'Холестерин',
          status: LabTestStatus.elevated,
          interpretationReasons: ['Повышен'],
          performedAt: '2025-01-15',
          value: LabTestValue(value: '6.8', unitLabel: 'ммоль/л'),
        ),
        LabTest(
          id: 'test_3',
          name: 'Белок',
          status: LabTestStatus.normal,
          interpretationReasons: [],
          performedAt: '2025-01-15',
          value: LabTestValue(value: '70', unitLabel: 'г/л'),
        ),
      ];

      final detail = LabReportDetail(
        report: report,
        tests: tests,
        patientAge: '43 лет',
        patientGender: 'MALE',
      );

      expect(detail.testsCount, equals(3));
      expect(detail.report.id, equals('report_123'));
      expect(detail.tests.length, equals(3));
      expect(detail.patientAge, equals('43 лет'));
      expect(detail.patientGender, equals('MALE'));

      // Тестируем статистику
      final stats = detail.getTestsStatistics();
      expect(stats['total'], equals(3));
      expect(stats['normal'], equals(2));
      expect(stats['abnormal'], equals(1)); // elevated считается как abnormal
      expect(stats['other'], equals(0));

      // Тестируем фильтрацию по статусу
      final elevatedTests = detail.getTestsByStatus(LabTestStatus.elevated);
      expect(elevatedTests.length, equals(1));
      expect(elevatedTests.first.name, equals('Холестерин'));

      final normalTests = detail.getTestsByStatus(LabTestStatus.normal);
      expect(normalTests.length, equals(2));
    });

    test('LabReportDetail JSON serialization should work correctly', () {
      final report = LabReport(
        id: 'report_123',
        analysisName: 'Тест',
        performedAt: '2025-01-15',

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 1,
      );

      final tests = [
        LabTest(
          id: 'test_1',
          name: 'Глюкоза',
          status: LabTestStatus.normal,
          interpretationReasons: [],
          performedAt: '2025-01-15',
          value: LabTestValue(value: '5.2', unitLabel: 'ммоль/л'),
        ),
      ];

      final detail = LabReportDetail(
        report: report,
        tests: tests,
        patientAge: '30 лет',
        patientGender: 'FEMALE',
      );

      // Тестируем toJson
      final json = detail.toJson();
      expect(json['id'], equals('report_123'));
      expect(json['patient']['age'], equals('30 лет'));
      expect(json['patient']['gender'], equals('FEMALE'));
      expect(json['testResults'], isA<Map<String, dynamic>>());
      expect(json['testResults']['totalCount'], equals(1));

      // Тестируем fromJson
      final detailFromJson = LabReportDetail.fromJson(json);
      expect(detailFromJson.report.id, equals('report_123'));
      expect(detailFromJson.tests.length, equals(1));
      expect(detailFromJson.patientAge, equals('30 лет'));
      expect(detailFromJson.patientGender, equals('FEMALE'));
    });

    test('getLabReportDocumentUrl should return document URL', () async {
      // Тестируем получение ссылки на документ
      const reportId = 'test-report-id';

      // Этот тест будет работать только с реальным API
      // В реальном приложении здесь должен быть mock
      try {
        final response = await labReportService.getLabReportDocumentUrl(reportId);

        // Проверяем структуру ответа
        expect(response, isA<ApiResponse<String>>());

        if (response.isSuccess) {
          expect(response.data, isNotNull);
          expect(response.data, isA<String>());
          expect(response.data!.isNotEmpty, isTrue);

          // Проверяем, что это похоже на URL
          final url = response.data!;
          expect(url.startsWith('http'), isTrue);

          // Дополнительная проверка для S3 URL (как в реальном API)
          if (url.contains('s3.') || url.contains('amazonaws.com') || url.contains('backblazeb2.com')) {
            print('✅ Получена валидная S3 ссылка: ${url.substring(0, 50)}...');
          }
        } else if (response.isNoData) {
          // Это нормально для тестового ID
          expect(response.status, equals('no_data'));
        }
      } catch (e) {
        // В тестовой среде API может быть недоступен
        expect(e, isA<ApiException>());
      }
    });

    test('getLabReportDocumentUrl should handle invalid report ID', () async {
      const invalidReportId = 'invalid-report-id';

      try {
        final response = await labReportService.getLabReportDocumentUrl(invalidReportId);

        // Ожидаем no_data для несуществующего отчета
        if (!response.isSuccess) {
          expect(response.isNoData, isTrue);
        }
      } catch (e) {
        // API может вернуть исключение для несуществующего ID
        expect(e, isA<ApiException>());
      }
    });
  });
}
