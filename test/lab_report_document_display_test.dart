import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/models/lab_report.dart';
import 'package:medstata_app/screens/lab_report_detail_screen.dart';

void main() {
  group('LabReportDetailScreen Document Display Tests', () {
    testWidgets('should show document info when document is available', (WidgetTester tester) async {
      // Создаем тестовые данные с документом
      final document = LabReportDocument(
        originalFileName: 'test_analysis.pdf',
        fileType: 'PDF',
        fileSizeBytes: 1024000,
      );

      final report = LabReport(
        id: 'test_report_id',
        performedAt: '2025-01-15T10:00:00Z',
        createdAt: '2025-01-15T11:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
        document: document,
      );

      // Создаем виджет
      await tester.pumpWidget(
        MaterialApp(
          home: LabReportDetailScreen(reportId: 'test_report_id'),
        ),
      );

      // Ждем завершения анимации
      await tester.pumpAndSettle();

      // Проверяем, что экран загрузился
      expect(find.byType(LabReportDetailScreen), findsOneWidget);
    });

    testWidgets('should show "Документ недоступен" when document is null', (WidgetTester tester) async {
      // Создаем тестовые данные без документа
      final report = LabReport(
        id: 'test_report_id',
        performedAt: '2025-01-15T10:00:00Z',
        createdAt: '2025-01-15T11:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
        document: null, // Документ отсутствует
      );

      // Создаем виджет
      await tester.pumpWidget(
        MaterialApp(
          home: LabReportDetailScreen(reportId: 'test_report_id'),
        ),
      );

      // Ждем завершения анимации
      await tester.pumpAndSettle();

      // Проверяем, что экран загрузился
      expect(find.byType(LabReportDetailScreen), findsOneWidget);
    });

    test('LabReportDocument should format file size correctly', () {
      final testCases = [
        {'bytes': null, 'expected': 'Неизвестно'},
        {'bytes': 500, 'expected': '500 Б'},
        {'bytes': 1024, 'expected': '1.0 КБ'},
        {'bytes': 1536, 'expected': '1.5 КБ'},
        {'bytes': 1024 * 1024, 'expected': '1.0 МБ'},
        {'bytes': 1024 * 1024 * 1024, 'expected': '1.0 ГБ'},
      ];

      for (final testCase in testCases) {
        final document = LabReportDocument(fileSizeBytes: testCase['bytes'] as int?);
        expect(document.formattedFileSize, equals(testCase['expected']));
      }
    });

    test('LabReportDocument should return correct file type icons', () {
      final testCases = [
        {'type': null, 'expected': '📄'},
        {'type': 'PDF', 'expected': '📄'},
        {'type': 'pdf', 'expected': '📄'},
        {'type': 'JPG', 'expected': '🖼️'},
        {'type': 'PNG', 'expected': '🖼️'},
        {'type': 'DOC', 'expected': '📝'},
        {'type': 'DOCX', 'expected': '📝'},
        {'type': 'XLS', 'expected': '📊'},
        {'type': 'XLSX', 'expected': '📊'},
        {'type': 'UNKNOWN', 'expected': '📄'},
      ];

      for (final testCase in testCases) {
        final document = LabReportDocument(fileType: testCase['type'] as String?);
        expect(document.fileTypeIcon, equals(testCase['expected']));
      }
    });

    test('LabReport should handle document field correctly', () {
      // Тест с документом
      final documentJson = {
        'originalFileName': 'analysis_report_2025_01_15.pdf',
        'fileType': 'PDF',
        'fileSizeBytes': 1024000,
      };

      final reportWithDocumentJson = {
        'id': 'test-id',
        'performedAt': '2025-01-15T10:00:00Z',
        'createdAt': '2025-01-15T11:00:00Z',
        'reportStatus': 'DONE',
        'testCount': 5,
        'document': documentJson,
      };

      final reportWithDocument = LabReport.fromJson(reportWithDocumentJson);

      expect(reportWithDocument.document, isNotNull);
      expect(reportWithDocument.document!.originalFileName, equals('analysis_report_2025_01_15.pdf'));
      expect(reportWithDocument.document!.fileType, equals('PDF'));
      expect(reportWithDocument.document!.fileSizeBytes, equals(1024000));

      // Тест без документа
      final reportWithoutDocumentJson = {
        'id': 'test-id',
        'performedAt': '2025-01-15T10:00:00Z',
        'createdAt': '2025-01-15T11:00:00Z',
        'reportStatus': 'DONE',
        'testCount': 5,
        // document отсутствует
      };

      final reportWithoutDocument = LabReport.fromJson(reportWithoutDocumentJson);

      expect(reportWithoutDocument.document, isNull);
    });

    test('LabReportDocument should handle empty/null fields gracefully', () {
      // Тест с пустыми полями
      final emptyDocument = LabReportDocument(
        originalFileName: null,
        fileType: null,
        fileSizeBytes: null,
      );

      expect(emptyDocument.originalFileName, isNull);
      expect(emptyDocument.fileType, isNull);
      expect(emptyDocument.fileSizeBytes, isNull);
      expect(emptyDocument.formattedFileSize, equals('Неизвестно'));
      expect(emptyDocument.fileTypeIcon, equals('📄'));

      // Тест с пустыми строками
      final emptyStringDocument = LabReportDocument(
        originalFileName: '',
        fileType: '',
        fileSizeBytes: 0,
      );

      expect(emptyStringDocument.originalFileName, equals(''));
      expect(emptyStringDocument.fileType, equals(''));
      expect(emptyStringDocument.fileSizeBytes, equals(0));
      expect(emptyStringDocument.formattedFileSize, equals('0 Б'));
      expect(emptyStringDocument.fileTypeIcon, equals('📄'));
    });
  });
}
