import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/models/lab_report.dart';

void main() {
  group('LabReportDocument Tests', () {
    test('should create LabReportDocument from JSON with all fields', () {
      final json = {
        'originalFileName': 'analysis_report_2025_01_15.pdf',
        'fileType': 'PDF',
        'fileSizeBytes': 1024000,
      };

      final document = LabReportDocument.fromJson(json);

      expect(document.originalFileName, equals('analysis_report_2025_01_15.pdf'));
      expect(document.fileType, equals('PDF'));
      expect(document.fileSizeBytes, equals(1024000));
    });

    test('should create LabReportDocument from JSON with missing fields', () {
      final json = <String, dynamic>{};

      final document = LabReportDocument.fromJson(json);

      expect(document.originalFileName, isNull);
      expect(document.fileType, isNull);
      expect(document.fileSizeBytes, isNull);
    });

    test('should format file size correctly', () {
      final testCases = [
        {'bytes': null, 'expected': 'Неизвестно'},
        {'bytes': 500, 'expected': '500 Б'},
        {'bytes': 1024, 'expected': '1.0 КБ'},
        {'bytes': 1536, 'expected': '1.5 КБ'},
        {'bytes': 1024 * 1024, 'expected': '1.0 МБ'},
        {'bytes': 1024 * 1024 * 1024, 'expected': '1.0 ГБ'},
      ];

      for (final testCase in testCases) {
        final document = LabReportDocument(fileSizeBytes: testCase['bytes'] as int?);
        expect(document.formattedFileSize, equals(testCase['expected']));
      }
    });

    test('should return correct file type icons', () {
      final testCases = [
        {'type': null, 'expected': '📄'},
        {'type': 'PDF', 'expected': '📄'},
        {'type': 'pdf', 'expected': '📄'},
        {'type': 'JPG', 'expected': '🖼️'},
        {'type': 'PNG', 'expected': '🖼️'},
        {'type': 'DOC', 'expected': '📝'},
        {'type': 'DOCX', 'expected': '📝'},
        {'type': 'XLS', 'expected': '📊'},
        {'type': 'XLSX', 'expected': '📊'},
        {'type': 'UNKNOWN', 'expected': '📄'},
      ];

      for (final testCase in testCases) {
        final document = LabReportDocument(fileType: testCase['type'] as String?);
        expect(document.fileTypeIcon, equals(testCase['expected']));
      }
    });

    test('should convert to JSON correctly', () {
      final document = LabReportDocument(
        originalFileName: 'test.pdf',
        fileType: 'PDF',
        fileSizeBytes: 1024,
      );

      final json = document.toJson();

      expect(json['originalFileName'], equals('test.pdf'));
      expect(json['fileType'], equals('PDF'));
      expect(json['fileSizeBytes'], equals(1024));
    });

    test('should handle equality correctly', () {
      final doc1 = LabReportDocument(
        originalFileName: 'test.pdf',
        fileType: 'PDF',
        fileSizeBytes: 1024,
      );

      final doc2 = LabReportDocument(
        originalFileName: 'test.pdf',
        fileType: 'PDF',
        fileSizeBytes: 1024,
      );

      final doc3 = LabReportDocument(
        originalFileName: 'different.pdf',
        fileType: 'PDF',
        fileSizeBytes: 1024,
      );

      expect(doc1, equals(doc2));
      expect(doc1, isNot(equals(doc3)));
      expect(doc1.hashCode, equals(doc2.hashCode));
    });
  });

  group('LabReport with Document Tests', () {
    test('should create LabReport with document from JSON', () {
      final json = {
        'id': 'test-id',
        'performedAt': '2025-01-15T10:00:00Z',
        'createdAt': '2025-01-15T11:00:00Z',
        'reportStatus': 'DONE',
        'testCount': 5,
        'document': {
          'originalFileName': 'analysis_report_2025_01_15.pdf',
          'fileType': 'PDF',
          'fileSizeBytes': 1024000,
        },
      };

      final report = LabReport.fromJson(json);

      expect(report.document, isNotNull);
      expect(report.document!.originalFileName, equals('analysis_report_2025_01_15.pdf'));
      expect(report.document!.fileType, equals('PDF'));
      expect(report.document!.fileSizeBytes, equals(1024000));
    });

    test('should create LabReport without document from JSON', () {
      final json = {
        'id': 'test-id',
        'performedAt': '2025-01-15T10:00:00Z',
        'createdAt': '2025-01-15T11:00:00Z',
        'reportStatus': 'DONE',
        'testCount': 5,
      };

      final report = LabReport.fromJson(json);

      expect(report.document, isNull);
    });

    test('should convert LabReport with document to JSON', () {
      final document = LabReportDocument(
        originalFileName: 'test.pdf',
        fileType: 'PDF',
        fileSizeBytes: 1024,
      );

      final report = LabReport(
        id: 'test-id',
        performedAt: '2025-01-15T10:00:00Z',
        createdAt: '2025-01-15T11:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 5,
        document: document,
      );

      final json = report.toJson();

      expect(json['document'], isNotNull);
      expect(json['document']['originalFileName'], equals('test.pdf'));
      expect(json['document']['fileType'], equals('PDF'));
      expect(json['document']['fileSizeBytes'], equals(1024));
    });
  });
}
