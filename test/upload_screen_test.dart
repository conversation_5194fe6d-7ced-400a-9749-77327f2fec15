import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:medstata_app/screens/upload_screen.dart';

void main() {
  group('UploadScreen Widget Tests', () {
    testWidgets('should create UploadScreen without crashing', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UploadScreen(),
        ),
      );

      // Проверяем, что экран создается без ошибок
      expect(find.byType(UploadScreen), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('should display correct title and description', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UploadScreen(),
        ),
      );

      // Проверяем заголовок
      expect(find.text('Загрузить анализы'), findsOneWidget);
      
      // Проверяем описание
      expect(find.text('Выберите фото из галереи или загрузите PDF файл с результатами ваших анализов'), findsOneWidget);
    });

    testWidgets('should display supported formats info', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UploadScreen(),
        ),
      );

      // Проверяем информацию о поддерживаемых форматах
      expect(find.text('Поддерживаемые форматы:'), findsOneWidget);
      expect(find.text('PDF, JPG, JPEG, PNG'), findsOneWidget);
    });

    testWidgets('should have upload button', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UploadScreen(),
        ),
      );

      // Проверяем наличие кнопки загрузки
      expect(find.text('Выбрать файл или фото'), findsOneWidget);
      expect(find.byIcon(Icons.add_photo_alternate), findsOneWidget);
    });

    testWidgets('should have upload icon in container', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UploadScreen(),
        ),
      );

      // Проверяем наличие иконки загрузки
      expect(find.byIcon(Icons.cloud_upload_outlined), findsOneWidget);
    });

    testWidgets('should be scrollable', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: UploadScreen(),
        ),
      );

      // Проверяем, что контент может прокручиваться
      expect(find.descendant(
        of: find.byType(UploadScreen),
        matching: find.byType(Column),
      ), findsAtLeastNWidgets(1));
      expect(find.byType(SafeArea), findsOneWidget);
    });
  });
}
