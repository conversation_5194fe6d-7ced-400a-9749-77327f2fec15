# Функциональность скачивания и просмотра документов

## Обзор

Добавлена функциональность для скачивания и просмотра документов лабораторных отчетов. Пользователи теперь могут:

1. **Скачивать документы** - сохранять файлы документов в локальное хранилище устройства
2. **Просматривать документы** - открывать документы для просмотра в внешних приложениях

## Реализованные изменения

### 1. Новый API метод

**Файл:** `lib/services/lab_report_service.dart`

Добавлен метод `getLabReportDocumentUrl(String reportId)`:
- Получает временную ссылку на документ по ID отчета
- Использует endpoint: `GET /api/lab-reports/{reportId}/document-url`
- Возвращает `ApiResponse<String>` с URL документа
- **Исправление**: API возвращает ссылку в поле `url`, а не `documentUrl`

```dart
Future<ApiResponse<String>> getLabReportDocumentUrl(String reportId)
```

### 2. Новый экран просмотра документов

**Файл:** `lib/screens/document_viewer_screen.dart`

Создан полнофункциональный экран для просмотра документов:
- Встроенный WebView для отображения PDF и изображений
- AppBar с заголовком документа
- Кнопка закрытия (красная иконка X)
- Кнопка обновления для перезагрузки документа
- Индикатор загрузки во время загрузки документа
- Экран ошибки с возможностью повторной попытки
- Автоматическое определение заголовка по имени файла

### 3. Обновленный экран деталей отчета

**Файл:** `lib/screens/lab_report_detail_screen.dart`

Добавлены методы:

#### `_downloadDocument(LabReportDocument document)`
- Получает временную ссылку на документ
- Скачивает файл в локальное хранилище
- Показывает прогресс загрузки
- Уведомляет пользователя об успешном скачивании

#### `_previewDocument(LabReportDocument document)`
- Получает временную ссылку на документ
- Открывает документ во встроенном WebView
- Поддерживает PDF и изображения
- Предоставляет кнопку закрытия и обновления

#### Вспомогательные методы:
- `_showErrorDialog(String message)` - показ ошибок
- `_getFileExtension(String? fileType)` - определение расширения файла
- `_openFileLocation(String filePath)` - попытка открыть папку с файлом

### 3. Новые зависимости

**Файл:** `pubspec.yaml`

Добавлены пакеты:
- `path_provider: ^2.1.1` - для работы с файловой системой
- `url_launcher: ^6.2.2` - для открытия внешних приложений
- `dio: ^5.4.0` - для скачивания файлов

### 4. Обновленные кнопки действий

В методе `_buildDocumentActions()` заменены заглушки на реальную функциональность:
- Кнопка "Скачать документ" теперь вызывает `_downloadDocument()`
- Кнопка "Просмотреть документ" теперь вызывает `_previewDocument()`

## Поддерживаемые типы файлов

- **PDF** - документы в формате PDF
- **Изображения** - JPG, JPEG, PNG, GIF
- **Другие** - любые файлы с автоматическим определением расширения

## Пользовательский опыт

### Скачивание документа:
1. Пользователь нажимает "Скачать документ"
2. Показывается диалог загрузки
3. Файл сохраняется в папку документов приложения
4. Показывается уведомление об успешном скачивании
5. Предлагается опция открыть папку с файлом

### Просмотр документа:
1. Пользователь нажимает "Просмотреть документ"
2. Показывается диалог загрузки
3. Открывается новый экран с встроенным WebView
4. Документ загружается и отображается в WebView
5. Пользователь может просматривать документ, масштабировать, прокручивать
6. Доступны кнопки "Закрыть" и "Обновить"
7. При ошибке показывается экран с возможностью повторной попытки

## Обработка ошибок

- Проверка доступности временной ссылки
- Обработка ошибок сети при скачивании
- Уведомления пользователя о проблемах
- Автоматическое закрытие диалогов загрузки при ошибках

## Тестирование

**Файл:** `test/lab_report_service_test.dart`

Добавлены тесты для нового API метода:
- Тест успешного получения URL документа
- Тест обработки несуществующего ID отчета
- Проверка структуры ответа API

## Безопасность

- Использование временных ссылок для доступа к документам
- Авторизация через Bearer токен
- Автоматическое обновление токенов при необходимости

## Совместимость

- **iOS** - полная поддержка скачивания и просмотра
- **Android** - полная поддержка скачивания и просмотра
- **Веб** - ограниченная поддержка (зависит от браузера)

## WebView функциональность

### Встроенный просмотр документов
Вместо открытия документов во внешних приложениях, теперь используется встроенный WebView:

**Преимущества**:
- Пользователь остается в приложении
- Единообразный интерфейс
- Контроль над процессом загрузки
- Возможность обработки ошибок

**Функции WebView экрана**:
- **Загрузка**: Индикатор прогресса во время загрузки документа
- **Навигация**: AppBar с заголовком и кнопками управления
- **Ошибки**: Специальный экран ошибки с возможностью повторной попытки
- **Управление**: Кнопки закрытия и обновления
- **Адаптивность**: Автоматическое масштабирование для разных размеров экрана

**Поддерживаемые форматы**:
- PDF документы (встроенный просмотр браузера)
- Изображения (JPG, PNG, GIF)
- Любые файлы, поддерживаемые WebView

## Исправления

### Проблема с парсингом ответа API
**Проблема**: При нажатии на кнопку "Скачать документ" отображалось сообщение "Не удалось получить ссылку на документ", хотя API возвращал корректный ответ.

**Причина**: API возвращает ссылку в поле `url`, а код ожидал её в поле `documentUrl`.

**Решение**: Исправлен метод `getLabReportDocumentUrl()` для корректного извлечения ссылки из поля `url`.

**Пример ответа API**:
```json
{
  "status": "ok",
  "url": "https://medstata-userfiles.s3.eu-central-003.backblazeb2.com/..."
}
```

## Примечания для разработчиков

1. Временные ссылки имеют ограниченное время жизни (обычно 15 минут)
2. Файлы сохраняются в папку документов приложения
3. Для открытия файлов используется системный обработчик
4. Необходимо обрабатывать случаи отсутствия подходящих приложений для просмотра
5. API возвращает ссылку в поле `url`, не `documentUrl`
