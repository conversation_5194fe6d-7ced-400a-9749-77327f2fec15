class AppConfig {
  static final String environment = const String.fromEnvironment(
    'ENV',
    defaultValue: 'DEV',
  );

  /// Для тестов можно переопределить вручную
  static String? _overrideEnv;

  static void overrideEnvironment(String env) {
    _overrideEnv = env;
  }

  static void resetEnvironmentOverride() {
    _overrideEnv = null;
  }

  static bool get isProd => (_overrideEnv ?? environment).toUpperCase() == 'PROD';

  static String get baseUrl =>
      isProd ? 'https://api.medstata.com' : 'https://medstata.avpuser.ru';

  /// API ключ для аутентификации запросов
  static String get apiKey => isProd
      ? 'e7e611cef8b7167e1f6318e9726f0d3d162cd6c93ca6615bbf8dceea2eec5df7'
      : 'e7e611cef8b7167e1f6318e9726f0d3d162cd6c93ca6615bbf8dceea2eec5df7';

  /// Путь к HTML-файлу авторизации
  static String get telegramAuthPage => '$baseUrl/telegram-auth-complete.html';

  /// Telegram bot ID по окружению
  static String get _telegramBotId => isProd ? '8044412847' : '8134568637';

  /// Telegram OAuth URL с dynamic origin и return_to
  static String get telegramOAuthUrl =>
      'https://oauth.telegram.org/auth?bot_id=$_telegramBotId'
          '&origin=$baseUrl'
          '&return_to=$baseUrl/telegram-auth-complete.html'
          '&embed=1'
          '&request_access=write';
}