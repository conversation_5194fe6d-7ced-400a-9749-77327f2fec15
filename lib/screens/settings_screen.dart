import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/auth_state_service.dart';
import 'auth_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AuthService _authService = AuthService();
  final AuthStateService _authStateService = AuthStateService();

  /// Выход из приложения
  Future<void> _logout() async {
    // Показываем диалог подтверждения
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Выйти из приложения'),
          content: const Text('Вы уверены, что хотите выйти из приложения?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Отмена'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Выйти'),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true) {
      try {
        // Выполняем выход
        _authService.logout();

        // Обновляем состояние авторизации
        _authStateService.setAuthenticationStatus(false);

        // Переходим к экрану авторизации
        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const AuthScreen()),
            (route) => false,
          );
        }
      } catch (e) {
        // Показываем ошибку
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Ошибка при выходе: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// Показывает информацию о разделе (заглушка)
  void _showComingSoon(String title) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$title - скоро будет доступно'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              
              // Заголовок
              const Text(
                'Настройки',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Список настроек
              Expanded(
                child: ListView(
                  children: [
                    _buildSettingsItem(
                      icon: Icons.person_outline,
                      title: 'Аккаунт',
                      subtitle: 'Управление профилем и данными',
                      onTap: () => _showComingSoon('Аккаунт'),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildSettingsItem(
                      icon: Icons.medical_information_outlined,
                      title: 'Данные пациента',
                      subtitle: 'Персональная медицинская информация',
                      onTap: () => _showComingSoon('Данные пациента'),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildSettingsItem(
                      icon: Icons.health_and_safety_outlined,
                      title: 'Приложение Здоровье',
                      subtitle: 'Интеграция с Apple Health',
                      onTap: () => _showComingSoon('Приложение Здоровье'),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Разделитель
                    Divider(color: Colors.grey.shade300),
                    
                    const SizedBox(height: 16),
                    
                    _buildSettingsItem(
                      icon: Icons.logout,
                      title: 'Выйти из приложения',
                      subtitle: 'Завершить текущую сессию',
                      onTap: _logout,
                      isDestructive: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isDestructive 
                ? Colors.red.shade50 
                : Colors.blue.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: isDestructive 
                ? Colors.red.shade600 
                : Colors.blue.shade600,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDestructive 
                ? Colors.red.shade700 
                : Colors.black87,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: Colors.grey.shade400,
        ),
        onTap: onTap,
      ),
    );
  }
}
