import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'lab_tests_screen.dart';
import 'lab_report_detail_screen.dart';
import 'auth_screen.dart';
import '../services/auth_service.dart';
import '../services/auth_state_service.dart';
import '../services/lab_test_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final LabTestService _labTestService = LabTestService();
  final AuthStateService _authStateService = AuthStateService();
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    // Подписываемся на изменения состояния авторизации
    _authStateService.addListener(_onAuthStateChanged);
  }

  @override
  void dispose() {
    _authStateService.removeListener(_onAuthStateChanged);
    _labTestService.dispose();
    super.dispose();
  }

  /// Обработчик изменения состояния авторизации
  void _onAuthStateChanged() {
    final isAuthenticated = _authStateService.isAuthenticated;
    print('🔄 HomeScreen: Состояние авторизации изменилось: $isAuthenticated');

    // Если пользователь вышел из системы, переходим к экрану авторизации
    if (!isAuthenticated && mounted) {
      print('🚪 HomeScreen: Пользователь вышел, переходим к AuthScreen');
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const AuthScreen()),
        (route) => false, // Удаляем все предыдущие экраны
      );
    }
  }

  /// Загружает файл с результатами анализов
  Future<void> _uploadLabTestFile() async {
    try {
      // Показываем диалог выбора файла
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        print('📁 Выбран файл: $fileName');

        // Показываем диалог с описанием
        final description = await _showDescriptionDialog(fileName);

        if (description != null) {
          setState(() {
            _isUploading = true;
          });

          try {
            // Загружаем файл
            final uploadResponse = await _labTestService.uploadLabTestFile(
              file,
              description: description.isNotEmpty ? description : null,
            );

            // Переходим на экран детального просмотра отчета
            if (mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LabReportDetailScreen(
                    reportId: uploadResponse.reportId,
                  ),
                ),
              );
            }
          } catch (e) {
            // Показываем ошибку
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('❌ Ошибка загрузки: $e'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 5),
                ),
              );
            }
          } finally {
            if (mounted) {
              setState(() {
                _isUploading = false;
              });
            }
          }
        }
      } else {
        print('ℹ️ Файл не выбран');
      }
    } catch (e) {
      print('❌ Ошибка выбора файла: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Ошибка выбора файла: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Показывает диалог для ввода описания файла
  Future<String?> _showDescriptionDialog(String fileName) async {
    final controller = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Описание файла'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Файл: $fileName'),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  labelText: 'Описание (необязательно)',
                  hintText: 'Например: Результаты анализов от 15.01.2025',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(controller.text),
              child: const Text('Загрузить'),
            ),
          ],
        );
      },
    );
  }

  /// Форматирует размер файла в читаемый вид
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes Б';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} КБ';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} МБ';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MedStata'),
        backgroundColor: Colors.blue.shade50,
        foregroundColor: Colors.blue.shade800,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_hospital,
              size: 80,
              color: Colors.blue.shade600,
            ),
            const SizedBox(height: 24),
            Text(
              'MedStata',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Медицинская аналитика',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 48),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LabTestsScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.science),
                label: const Text('Лабораторные анализы'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Функция в разработке'),
                    ),
                  );
                },
                icon: const Icon(Icons.person),
                label: const Text('Данные пациента'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue.shade600,
                  side: BorderSide(color: Colors.blue.shade600),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Кнопка загрузки файлов
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isUploading ? null : () => _uploadLabTestFile(),
                icon: _isUploading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Icon(Icons.upload_file),
                label: Text(_isUploading
                    ? 'Загрузка файла...'
                    : 'Загрузить результаты анализов'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),

            const SizedBox(height: 32),

            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () async {
                  // Показываем диалог подтверждения
                  final shouldLogout = await showDialog<bool>(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('Выход'),
                        content: const Text('Вы уверены, что хотите выйти из приложения?'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text('Отмена'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red,
                            ),
                            child: const Text('Выйти'),
                          ),
                        ],
                      );
                    },
                  );

                  // Если пользователь подтвердил выход
                  if (shouldLogout == true) {
                    try {
                      // Выполняем logout через AuthService
                      final authService = AuthService();
                      authService.logout();

                      // Обновляем состояние через AuthStateService
                      final authStateService = AuthStateService();
                      authStateService.logout();

                      // Показываем сообщение об успешном выходе
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Вы успешно вышли из системы'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    } catch (e) {
                      // Показываем ошибку, если что-то пошло не так
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Ошибка при выходе: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  }
                },
                icon: const Icon(Icons.logout),
                label: const Text('Выйти'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red.shade600,
                  side: BorderSide(color: Colors.red.shade600),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}