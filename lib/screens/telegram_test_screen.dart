import 'package:flutter/material.dart';
import '../medstata_api.dart';

/// Экран для тестирования интеграции с Telegram WebApp
class TelegramTestScreen extends StatefulWidget {
  const TelegramTestScreen({super.key});

  @override
  State<TelegramTestScreen> createState() => _TelegramTestScreenState();
}

class _TelegramTestScreenState extends State<TelegramTestScreen> {
  final TelegramService _telegramService = TelegramService.instance;
  final AppleService _appleService = AppleService.instance;
  final AuthService _authService = AuthService();

  final _userIdController = TextEditingController(text: '52952911');
  final _firstNameController = TextEditingController(text: 'John');
  final _lastNameController = TextEditingController(text: 'Doe');
  final _usernameController = TextEditingController(text: 'johndoe');

  // Apple Sign In поля
  final _appleEmailController = TextEditingController(text: '<EMAIL>');
  final _appleFirstNameController = TextEditingController(text: 'Apple');
  final _appleLastNameController = TextEditingController(text: 'User');

  bool _isLoading = false;
  String? _result;
  bool _isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentState();
  }

  void _checkCurrentState() {
    setState(() {
      _isAuthenticated = _authService.isAuthenticated;
      if (_isAuthenticated) {
        _result = 'Пользователь уже авторизован\nUser ID: ${_authService.userId}';
      }
    });
  }

  Future<void> _simulateTelegramWebApp() async {
    setState(() {
      _isLoading = true;
      _result = null;
    });

    try {
      final userId = int.tryParse(_userIdController.text);
      if (userId == null) {
        throw Exception('Неверный User ID');
      }

      // Симулируем Telegram WebApp
      await _telegramService.simulateTelegramWebApp(
        userId: userId,
        firstName: _firstNameController.text.isNotEmpty ? _firstNameController.text : null,
        lastName: _lastNameController.text.isNotEmpty ? _lastNameController.text : null,
        username: _usernameController.text.isNotEmpty ? _usernameController.text : null,
      );

      // Выполняем авторизацию
      final authResponse = await _authService.loginWithTelegramWebApp();

      setState(() {
        _isAuthenticated = true;
        _result = '''✅ Telegram авторизация успешна!

User ID: ${authResponse.userId}
Access Token: ${authResponse.accessToken.substring(0, 20)}...
Refresh Token: ${authResponse.refreshToken.substring(0, 20)}...

Теперь можно использовать API для получения данных пациента и анализов.''';
      });

      // Показываем уведомление
      _telegramService.showAlert('Авторизация успешна!');

    } catch (e) {
      setState(() {
        _result = '❌ Ошибка: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _simulateAppleSignIn() async {
    setState(() {
      _isLoading = true;
      _result = null;
    });

    try {
      // Инициализируем Apple Service
      await _appleService.initialize();

      // Симулируем Apple Sign In
      final appleRequest = await _appleService.simulateAppleSignIn(
        email: _appleEmailController.text.isNotEmpty ? _appleEmailController.text : null,
        firstName: _appleFirstNameController.text.isNotEmpty ? _appleFirstNameController.text : null,
        lastName: _appleLastNameController.text.isNotEmpty ? _appleLastNameController.text : null,
      );

      if (appleRequest == null) {
        throw Exception('Не удалось симулировать Apple Sign In');
      }

      // Выполняем авторизацию
      final authResponse = await _authService.appleLogin(appleRequest);

      setState(() {
        _isAuthenticated = true;
        _result = '''✅ Apple авторизация успешна!

User ID: ${authResponse.userId}
Access Token: ${authResponse.accessToken.substring(0, 20)}...
Refresh Token: ${authResponse.refreshToken.substring(0, 20)}...

Email: ${appleRequest.email}
Name: ${appleRequest.firstName} ${appleRequest.lastName}

Теперь можно использовать API для получения данных пациента и анализов.''';
      });

    } catch (e) {
      setState(() {
        _result = '❌ Ошибка Apple Sign In: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testPatientAPI() async {
    if (!_isAuthenticated) {
      setState(() {
        _result = '❌ Сначала выполните авторизацию';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'Тестируем Patient API...';
    });

    try {
      final patientService = PatientService();
      final response = await patientService.getPatient();
      
      if (response.isSuccess && response.data != null) {
        final patient = response.data!;
        setState(() {
          _result = '''✅ Patient API работает!

Возраст: ${patient.age} лет
Пол: ${patient.gender.value}
Статус: ${response.status}''';
        });
      } else {
        setState(() {
          _result = '''⚠️ Patient API ответил:

Статус: ${response.status}
Сообщение: ${response.message}''';
        });
      }
    } catch (e) {
      setState(() {
        _result = '❌ Ошибка Patient API: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testLabTestsAPI() async {
    if (!_isAuthenticated) {
      setState(() {
        _result = '❌ Сначала выполните авторизацию';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'Тестируем Lab Tests API...';
    });

    try {
      print('🧪 TelegramTestScreen: Тестируем Lab Tests API');
      final labTestService = LabTestService();
      final response = await labTestService.getLabTests();
      
      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        setState(() {
          _result = '''✅ Lab Tests API работает!

Всего анализов: ${data.totalCount}
Нормальных: ${data.normalCount}
Отклонений: ${data.abnormalCount}
Других: ${data.otherCount}

Категорий: ${data.groupLabTests.keys.length}''';
        });
      } else {
        setState(() {
          _result = '''⚠️ Lab Tests API ответил:

Статус: ${response.status}
Сообщение: ${response.message}''';
        });
      }
    } catch (e) {
      setState(() {
        _result = '❌ Ошибка Lab Tests API: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testRefreshToken() async {
    if (!_isAuthenticated) {
      setState(() {
        _result = '❌ Сначала выполните авторизацию';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'Тестируем обновление токена...';
    });

    try {
      print('🧪 TelegramTestScreen: Тестируем refresh token');

      // Показываем текущие токены
      final currentAccessToken = _authService.accessToken;
      final currentRefreshToken = _authService.refreshToken;
      final accessExpiresAt = _authService.accessTokenExpiresAt;
      final refreshExpiresAt = _authService.refreshTokenExpiresAt;

      print('📄 Текущий access token: ${currentAccessToken?.substring(0, 20)}...');
      print('📄 Текущий refresh token: ${currentRefreshToken?.substring(0, 20)}...');
      print('⏰ Access токен истекает: $accessExpiresAt');
      print('⏰ Refresh токен истекает: $refreshExpiresAt');

      // Принудительно обновляем токен
      await _authService.refreshAccessToken();

      // Показываем новые токены
      final newAccessToken = _authService.accessToken;
      final newRefreshToken = _authService.refreshToken;
      final newAccessExpiresAt = _authService.accessTokenExpiresAt;
      final newRefreshExpiresAt = _authService.refreshTokenExpiresAt;

      setState(() {
        _result = '''✅ Refresh Token работает!

Старый access token: ${currentAccessToken?.substring(0, 20)}...
Новый access token: ${newAccessToken?.substring(0, 20)}...

Старое время истечения: $accessExpiresAt
Новое время истечения: $newAccessExpiresAt

Токены успешно обновлены!''';
      });

    } catch (e) {
      print('❌ TelegramTestScreen: Refresh token error: $e');
      setState(() {
        _result = '❌ Ошибка обновления токена: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _logout() {
    _authService.logout();
    setState(() {
      _isAuthenticated = false;
      _result = 'Выход выполнен';
    });
  }

  void _showDebugInfo() {
    final debugInfo = _telegramService.getDebugInfo();
    setState(() {
      _result = '''🔍 Отладочная информация:

Инициализирован: ${debugInfo['initialized']}
Доступен: ${debugInfo['available']}
Платформа: ${debugInfo['platform']}
Версия: ${debugInfo['version']}

Пользователь: ${debugInfo['user'] != null ? 'Есть' : 'Нет'}
InitData: ${debugInfo['initData']}''';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Telegram WebApp Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Поля ввода данных пользователя Telegram
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Данные пользователя Telegram',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _userIdController,
                      decoration: const InputDecoration(
                        labelText: 'User ID',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _firstNameController,
                      decoration: const InputDecoration(
                        labelText: 'Имя',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _lastNameController,
                      decoration: const InputDecoration(
                        labelText: 'Фамилия',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Поля ввода данных пользователя Apple
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Данные пользователя Apple',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: _appleEmailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _appleFirstNameController,
                      decoration: const InputDecoration(
                        labelText: 'Имя',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _appleLastNameController,
                      decoration: const InputDecoration(
                        labelText: 'Фамилия',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Кнопки действий
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _simulateTelegramWebApp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Симулировать Telegram WebApp'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _simulateAppleSignIn,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Симулировать Apple Sign In'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testPatientAPI,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Тест Patient API'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testLabTestsAPI,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Тест Lab Tests API'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _testRefreshToken,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Тест Refresh Token'),
                ),
                ElevatedButton(
                  onPressed: _logout,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Выйти'),
                ),
                ElevatedButton(
                  onPressed: _showDebugInfo,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Debug Info'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Результат
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Результат',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _result ?? 'Нажмите кнопку для тестирования',
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _userIdController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _usernameController.dispose();
    _appleEmailController.dispose();
    _appleFirstNameController.dispose();
    _appleLastNameController.dispose();
    _authService.dispose();
    super.dispose();
  }
}
