import 'package:flutter/material.dart';
import '../medstata_api.dart';

class LabTestDetailScreen extends StatefulWidget {
  final LabTest labTest;

  const LabTestDetailScreen({
    super.key,
    required this.labTest,
  });

  @override
  State<LabTestDetailScreen> createState() => _LabTestDetailScreenState();
}

class _LabTestDetailScreenState extends State<LabTestDetailScreen> {
  final LabTestService _labTestService = LabTestService();
  LabTestDetail? _testDetail;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadTestDetail();
  }

  @override
  void dispose() {
    _labTestService.dispose();
    super.dispose();
  }

  Future<void> _loadTestDetail() async {
    try {
      final response = await _labTestService.getLabTest(widget.labTest.id);
      
      if (response.isSuccess) {
        setState(() {
          _testDetail = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? 'Не удалось загрузить детали анализа';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка загрузки: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(
          widget.labTest.name,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.red.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _errorMessage = null;
                });
                _loadTestDetail();
              },
              child: const Text('Повторить'),
            ),
          ],
        ),
      );
    }

    if (_testDetail == null) {
      return const Center(
        child: Text('Нет данных'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMainInfoCard(),
          const SizedBox(height: 16),
          _buildValueCard(),
          const SizedBox(height: 16),
          if (widget.labTest.interpretationReasons.isNotEmpty) ...[
            _buildInterpretationCard(),
            const SizedBox(height: 16),
          ],
          if (_testDetail?.history.isNotEmpty == true) ...[
            _buildHistoryCard(),
            const SizedBox(height: 16),
          ],
          _buildAdditionalInfoCard(),
          const SizedBox(height: 24),
          _buildLegalDisclaimerText(),
        ],
      ),
    );
  }

  Widget _buildMainInfoCard() {
    final statusColor = _getStatusColor(widget.labTest.status);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.labTest.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              _buildStatusIndicator(widget.labTest.status, statusColor),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Дата анализа: ${widget.labTest.performedAt}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValueCard() {
    final statusColor = _getStatusColor(widget.labTest.status);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Результат',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Значение:',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),

              const SizedBox(width: 8), // небольшой отступ между колонками (по желанию)
              Expanded(
                child: Text(
                '${widget.labTest.value.value} ${widget.labTest.value.unitLabel}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: statusColor,
                ),
                overflow: TextOverflow.fade,
                softWrap: false, textAlign: TextAlign.right, // ← прижатие к правому краю

                ),
              ),
            ],
          ),
          if (widget.labTest.value.referenceRange != null) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Референсные значения:',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),

                const SizedBox(width: 8), // небольшой отступ между колонками (по желанию)
                Expanded(
                child: Text(
                  (widget.labTest.value.referenceRange?.trim().isEmpty ?? true)
                      ? '-'
                      : widget.labTest.value.referenceRange!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.fade,
                  softWrap: false,
                  textAlign: TextAlign.right, // ← прижатие к правому краю
                ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Статус:',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),

              const SizedBox(width: 8), // небольшой отступ между колонками (по желанию)
              Expanded(
              child: Text(
                _getStatusText(widget.labTest.status),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: statusColor,
                ),
                overflow: TextOverflow.fade,
                softWrap: false,
                textAlign: TextAlign.right, // ← прижатие к правому краю
              ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInterpretationCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Возможные причины',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.labTest.interpretationReasons.map((reason) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.only(top: 6, right: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade400,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    reason,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildHistoryCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'История анализов',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          ...(_testDetail?.history ?? []).map((historyItem) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${historyItem.value.value} ${historyItem.value.unitLabel}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        historyItem.performedAt,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _getStatusColor(historyItem.status),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Дополнительная информация',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('ID анализа', widget.labTest.id),
          const SizedBox(height: 12),
          _buildInfoRow('Всего записей в истории', '${_testDetail?.history.length ?? 0}'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusIndicator(LabTestStatus status, Color statusColor) {
    final isNormal = status == LabTestStatus.normal;

    if (isNormal) {
      return Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          color: statusColor,
          shape: BoxShape.circle,
        ),
      );
    } else {
      return Icon(
        status == LabTestStatus.elevated
            ? Icons.arrow_drop_up
            : Icons.arrow_drop_down,
        color: statusColor,
        size: 24,
      );
    }
  }

  Color _getStatusColor(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return Colors.green;
      case LabTestStatus.elevated:
      case LabTestStatus.low:
        return Colors.orange;
      case LabTestStatus.other:
        return Colors.blue;
    }
  }

  String _getStatusText(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return 'Норма';
      case LabTestStatus.elevated:
        return 'Повышен';
      case LabTestStatus.low:
        return 'Понижен';
      case LabTestStatus.other:
        return 'Прочее';
    }
  }

  Widget _buildLegalDisclaimerText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Text(
        'Приложение MedStata предназначено исключительно для информационных целей и не представляет собой медицинское устройство. Оно не является заменой профессиональной медицинской консультации, диагностики или лечения.',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey.shade500,
          height: 1.4,
        ),
        textAlign: TextAlign.left,
      ),
    );
  }
}
