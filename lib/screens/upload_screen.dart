import 'dart:io';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'lab_report_detail_screen.dart';
import '../services/lab_test_service.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  final LabTestService _labTestService = LabTestService();
  final ImagePicker _imagePicker = ImagePicker();
  bool _isUploading = false;

  @override
  void dispose() {
    _labTestService.dispose();
    super.dispose();
  }

  /// Выбирает изображение из галереи
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85, // Сжимаем изображение для экономии места
      );

      if (image != null) {
        final file = File(image.path);
        final fileName = image.name;

        print('📷 Выбрано изображение из галереи: $fileName');

        // Показываем диалог с описанием
        final description = await _showDescriptionDialog(fileName);

        if (description != null) {
          await _uploadFile(file, fileName, description);
        }
      } else {
        print('ℹ️ Изображение не выбрано');
      }
    } catch (e) {
      print('❌ Ошибка выбора изображения: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Ошибка выбора изображения: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Загружает файл с результатами анализов
  Future<void> _uploadLabTestFile() async {
    try {
      // Показываем диалог выбора файла
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;

        print('📁 Выбран файл: $fileName');

        // Показываем диалог с описанием
        final description = await _showDescriptionDialog(fileName);

        if (description != null) {
          await _uploadFile(file, fileName, description);
        }
      } else {
        print('ℹ️ Файл не выбран');
      }
    } catch (e) {
      print('❌ Ошибка выбора файла: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Ошибка выбора файла: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Загружает файл на сервер
  Future<void> _uploadFile(File file, String fileName, String description) async {
    setState(() {
      _isUploading = true;
    });

    try {
      // Загружаем файл
      final uploadResponse = await _labTestService.uploadLabTestFile(
        file,
        description: description.isNotEmpty ? description : null,
      );

      // Переходим на экран детального просмотра отчета
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => LabReportDetailScreen(
              reportId: uploadResponse.reportId,
            ),
          ),
        );
      }
    } catch (e) {
      // Показываем ошибку
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Ошибка загрузки: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  /// Показывает диалог выбора источника файла
  Future<void> _showSourceSelectionDialog() async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Заголовок
            const Text(
              'Выберите источник',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),

            // Кнопка выбора из галереи
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.photo_library,
                  color: Colors.green.shade600,
                ),
              ),
              title: const Text('Выбрать из галереи'),
              subtitle: const Text('Фотографии из вашей галереи'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery();
              },
            ),

            const SizedBox(height: 8),

            // Кнопка выбора файла
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.folder_open,
                  color: Colors.blue.shade600,
                ),
              ),
              title: const Text('Выбрать файл'),
              subtitle: const Text('PDF, JPG, PNG файлы'),
              onTap: () {
                Navigator.pop(context);
                _uploadLabTestFile();
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// Показывает диалог для ввода описания файла
  Future<String?> _showDescriptionDialog(String fileName) async {
    final TextEditingController controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Описание файла'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Файл: $fileName',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  labelText: 'Описание (необязательно)',
                  hintText: 'Например: Анализы от 15.01.2024',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                textCapitalization: TextCapitalization.sentences,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(controller.text),
              child: const Text('Загрузить'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              
              // Заголовок
              const Text(
                'Загрузить анализы',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Описание
              Text(
                'Выберите фото из галереи или загрузите PDF файл с результатами ваших анализов',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              // Иконка загрузки
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.cloud_upload_outlined,
                  size: 60,
                  color: Colors.blue.shade600,
                ),
              ),
              
              const SizedBox(height: 48),
              
              // Поддерживаемые форматы
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  children: [
                    const Text(
                      'Поддерживаемые форматы:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'PDF, JPG, JPEG, PNG',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Кнопка загрузки
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isUploading ? null : _showSourceSelectionDialog,
                  icon: _isUploading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.add_photo_alternate),
                  label: Text(_isUploading ? 'Загрузка...' : 'Выбрать файл или фото'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    textStyle: const TextStyle(fontSize: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
