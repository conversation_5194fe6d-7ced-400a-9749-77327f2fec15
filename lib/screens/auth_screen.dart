import 'package:flutter/material.dart';
import '../medstata_api.dart';
import '../services/telegram_webview_service.dart';
import '../services/auth_state_service.dart';
import 'main_navigation_screen.dart';

/// Экран авторизации с Apple ID
class AuthScreen extends StatefulWidget {
  final VoidCallback? onAuthSuccess;

  const AuthScreen({
    super.key,
    this.onAuthSuccess,
  });

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final AppleService _appleService = AppleService.instance;
  final AuthService _authService = AuthService();
  final AuthStateService _authStateService = AuthStateService();

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeAppleService();
    // Подписываемся на изменения состояния авторизации
    _authStateService.addListener(_onAuthStateChanged);
  }

  @override
  void dispose() {
    _authStateService.removeListener(_onAuthStateChanged);
    super.dispose();
  }

  /// Обработчик изменения состояния авторизации
  void _onAuthStateChanged() {
    final isAuthenticated = _authStateService.isAuthenticated;
    print('🔄 AuthScreen: Состояние авторизации изменилось: $isAuthenticated');

    // Если пользователь авторизовался, переходим к главному экрану
    if (isAuthenticated && mounted) {
      print('✅ AuthScreen: Пользователь авторизован, переходим к MainNavigationScreen');

      // Сначала вызываем callback, если он есть
      widget.onAuthSuccess?.call();

      // Затем переходим к главному экрану
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
        (route) => false, // Удаляем все предыдущие экраны
      );
    }
  }

  Future<void> _initializeAppleService() async {
    await _appleService.initialize();
  }

  Future<void> _signInWithApple() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Выполняем Apple Sign In
      final appleRequest = await _appleService.signIn();
      
      if (appleRequest == null) {
        throw Exception('Не удалось получить данные Apple Sign In');
      }

      // Авторизуемся на сервере
      final authResponse = await _authService.appleLogin(appleRequest);

      print('✅ Apple авторизация успешна');
      print('   User ID: ${authResponse.userId}');
      print('🔍 AuthScreen: Проверяем установку токена...');
      print('   Access Token: ${_authService.accessToken?.substring(0, 20)}...');
      print('   Refresh Token: ${_authService.refreshToken?.substring(0, 20)}...');

      // Вызываем callback об успешной авторизации
      widget.onAuthSuccess?.call();
      
    } catch (e) {
      print('❌ Apple авторизация не удалась: $e');
      setState(() {
        _errorMessage = _getErrorMessage(e);
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getErrorMessage(dynamic error) {
    final errorStr = error.toString();

    if (errorStr.contains('canceled') || errorStr.contains('отменена')) {
      return 'Авторизация отменена';
    } else if (errorStr.contains('invalid_token')) {
      return 'Ошибка токена Apple';
    } else if (errorStr.contains('network') || errorStr.contains('timeout')) {
      return 'Проблемы с сетью. Проверьте подключение к интернету';
    } else {
      return 'Ошибка авторизации. Попробуйте еще раз';
    }
  }

  Future<void> _signInWithTelegram() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Открываем WebView для авторизации через Telegram
      final authResponse = await TelegramWebViewService.authenticateWithTelegram(context);

      if (authResponse == null) {
        throw Exception('Авторизация через Telegram была отменена');
      }

      print('✅ Telegram авторизация успешна');
      print('   User ID: ${authResponse.userId}');
      print('🔍 AuthScreen: Проверяем установку токена...');
      print('   Access Token: ${authResponse.accessToken.substring(0, 20)}...');
      print('   Refresh Token: ${authResponse.refreshToken != null ? authResponse.refreshToken!.substring(0, 20) : 'null'}...');

      // Вызываем callback об успешной авторизации
      widget.onAuthSuccess?.call();

    } catch (e) {
      print('❌ Telegram авторизация не удалась: $e');
      setState(() {
        _errorMessage = _getErrorMessage(e);
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Верхняя часть с логотипом и текстом
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(height: MediaQuery.of(context).size.height * 0.05),
                    
                    // Заголовок
                    const Text(
                      'Добро пожаловать\nв MedStata',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                        height: 1.2,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Подзаголовок
                    const Text(
                      'Загружайте анализы, получайте\nрасшифровки и следите за своим\nздоровьем.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                        height: 1.4,
                      ),
                    ),
                    
                    const SizedBox(height: 40),

                    // Иллюстрация
                    ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.asset(
                        'assets/images/auth_screen.png',
                        width: 240,
                        height: 160,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback если изображение не найдено
                          return Container(
                            width: 240,
                            height: 160,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: Colors.grey.shade50,
                            ),
                            child: const Icon(
                              Icons.image,
                              size: 48,
                              color: Colors.grey,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),

                // Нижняя часть с кнопками
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: MediaQuery.of(context).size.height * 0.05),
                    // Кнопка Apple Sign In
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _signInWithApple,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                          elevation: 0,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.apple,
                                    size: 24,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Продолжить с Apple',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Кнопка Telegram Sign In
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _signInWithTelegram,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0088CC), // Telegram blue
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                          elevation: 0,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 24,
                                    height: 24,
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.telegram,
                                      size: 16,
                                      color: Color(0xFF0088CC),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    'Продолжить с Telegram',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Текст о конфиденциальности
                    const Text(
                      'Мы не сохраняем лишнего. Только\nваше согласие — и всё готово!',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        height: 1.4,
                      ),
                    ),
                    
                    if (_errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red.shade600,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: Colors.red.shade700,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    
                    const SizedBox(height: 20),
                  ],
                ),

                SizedBox(height: MediaQuery.of(context).size.height * 0.05),
              ],
            ),
          ),
        ),
      ),
    );
  }
}