import 'package:flutter/material.dart';
import '../medstata_api.dart';
import 'lab_test_detail_screen.dart';

class LabTestsScreen extends StatefulWidget {
  const LabTestsScreen({super.key});

  @override
  State<LabTestsScreen> createState() => _LabTestsScreenState();
}

class _LabTestsScreenState extends State<LabTestsScreen> {
  final LabTestService _labTestService = LabTestService();
  GroupedLabTests? _labTestsData;
  bool _isLoading = true;
  String? _errorMessage;
  bool _showOnlyAbnormal = false;
  int _selectedTabIndex = 0; // 0 - Биомаркеры, 1 - Документы

  @override
  void initState() {
    super.initState();
    _loadLabTests();
  }

  @override
  void dispose() {
    _labTestService.dispose();
    super.dispose();
  }

  Future<void> _loadLabTests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _labTestService.getLabTests();

      if (response.isSuccess) {
        setState(() {
          _labTestsData = response.data;
          _isLoading = false;
        });
      } else if (response.isNoData) {
        setState(() {
          _errorMessage = 'Нет данных анализов для авторизованного пользователя';
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? 'Неизвестная ошибка';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка при загрузке данных: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            if (_selectedTabIndex == 0) _buildPeriodSelector(),
            Expanded(child: _buildBody()),
          ],
        ),
      ),
    );
  }

  /// Версия для использования в MainNavigationScreen без Scaffold и кнопки "Назад"
  Widget buildContent() {
    return Container(
      color: Colors.grey.shade50,
      child: Column(
        children: [
          _buildHeaderWithoutBackButton(),
          _buildTabBar(),
          if (_selectedTabIndex == 0) _buildPeriodSelector(),
          Expanded(child: _buildBody()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_ios),
          ),
          const Spacer(),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.search, color: Colors.red),
          ),
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: const Icon(Icons.more_horiz),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderWithoutBackButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Spacer(),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.search, color: Colors.red),
          ),
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: const Icon(Icons.more_horiz),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        children: [
          Expanded(
            child: Material(
              color: Colors.transparent,
              child: ConstrainedBox(
                constraints: const BoxConstraints(minHeight: 48),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedTabIndex = 0;
                    });
                  },
                  borderRadius: BorderRadius.circular(25),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: _selectedTabIndex == 0
                        ? BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          )
                        : null,
                    child: Center(
                      child: Text(
                        'Биомаркеры',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: _selectedTabIndex == 0 ? FontWeight.w600 : FontWeight.w500,
                          fontSize: 16,
                          color: _selectedTabIndex == 0 ? Colors.black : Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: Material(
              color: Colors.transparent,
              child: ConstrainedBox(
                constraints: const BoxConstraints(minHeight: 48),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedTabIndex = 1;
                    });
                  },
                  borderRadius: BorderRadius.circular(25),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: _selectedTabIndex == 1
                        ? BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          )
                        : null,
                    child: Center(
                      child: Text(
                        'Документы',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: _selectedTabIndex == 1 ? FontWeight.w600 : FontWeight.w500,
                          fontSize: 16,
                          color: _selectedTabIndex == 1 ? Colors.black : Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const Text(
            'За последний год',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Icon(Icons.keyboard_arrow_down),
          const Spacer(),
          IconButton(
            onPressed: _loadLabTests,
            icon: const Icon(Icons.refresh, color: Colors.red),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_selectedTabIndex == 1) {
      // Показываем экран документов
      return const LabReportsScreen();
    }

    // Показываем экран биомаркеров (оригинальная логика)
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Загрузка анализов...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadLabTests,
              child: const Text('Повторить'),
            ),
          ],
        ),
      );
    }

    if (_labTestsData == null) {
      return const Center(
        child: Text('Нет данных для отображения'),
      );
    }

    return Scrollbar(
      thumbVisibility: true,
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        physics: const BouncingScrollPhysics(),
        children: _buildTestsList(),
      ),
    );
  }

  List<Widget> _buildTestsList() {
    final data = _labTestsData!;
    final List<Widget> widgets = [];

    for (final entry in data.groupLabTests.entries) {
      final category = entry.key;
      final tests = entry.value;

      // Фильтруем анализы по статусу если включен фильтр
      final filteredTests = _showOnlyAbnormal
          ? tests.where((test) =>
              test.status == LabTestStatus.elevated ||
              test.status == LabTestStatus.low ||
              test.status == LabTestStatus.elevated || test.status == LabTestStatus.low
            ).toList()
          : tests;

      // Пропускаем категорию если нет анализов после фильтрации
      if (filteredTests.isEmpty) continue;

      // Добавляем заголовок категории
      widgets.add(_buildCategoryHeader(category));
      widgets.add(const SizedBox(height: 16));

      // Добавляем анализы этой категории
      for (final test in filteredTests) {
        widgets.add(_buildTestCard(test));
        widgets.add(const SizedBox(height: 12));
      }

      widgets.add(const SizedBox(height: 8));
    }

    return widgets;
  }

  Widget _buildCategoryHeader(String category) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 8),
      child: Text(
        category.toUpperCase(),
        style: TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          color: Colors.grey.shade600,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildTestCard(LabTest test) {
    final statusColor = _getStatusColor(test.status);
    
    return InkWell(
      onTap: () => _showTestDetail(test),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // График/индикатор слева
            _buildTestIndicator(test),
            const SizedBox(width: 16),
            // Информация о тесте
            Expanded(
              child: Text(
                test.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Значение и статус справа
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${test.value.value} ${test.value.unitLabel}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                    const SizedBox(width: 4),
                    _buildStatusIndicatorIcon(test, statusColor),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDate(test.performedAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildStatusIndicatorIcon(LabTest test, Color statusColor) {
    final isNormal = test.status == LabTestStatus.normal;

    return SizedBox(
      width: 24,
      height: 24,
      child: Center(
        child: isNormal
            ? Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: statusColor,
            shape: BoxShape.circle,
          ),
        )
            : Text(
          test.status == LabTestStatus.elevated ? '▲' : '▼',
          style: TextStyle(
            fontSize: 16,
            color: statusColor,
            fontWeight: FontWeight.w900, // жирная стрелка
          ),
        ),
      ),
    );
  }



  Widget _buildTestIndicator(LabTest test) {
    final statusColor = _getStatusColor(test.status);
    final isNormal = test.status == LabTestStatus.normal;

    return Container(
      width: 60,
      height: 40,
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // Фоновая линия
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Container(
              height: 2,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
          // Индикатор значения
          Positioned(
            bottom: 6,
            left: isNormal ? 24 : (test.status == LabTestStatus.elevated ? 40 : 12),
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
          ),

        ],
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      // Пытаемся парсить разные форматы дат
      if (dateString.contains('мар') || dateString.contains('янв') || dateString.contains('нояб')) {
        return dateString; // Уже в нужном формате
      }

      final date = DateTime.parse(dateString);
      final months = [
        'янв', 'фев', 'мар', 'апр', 'май', 'июн',
        'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'
      ];
      return '${date.day} ${months[date.month - 1]} ${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  Color _getStatusColor(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return Colors.green;
      case LabTestStatus.elevated:
      case LabTestStatus.low:
        return Colors.orange;
      case LabTestStatus.other:
        return Colors.blue;
    }
  }

  void _showTestDetail(LabTest test) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LabTestDetailScreen(labTest: test),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Индикатор перетаскивания
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // Группировать
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    Icons.view_list,
                    size: 16,
                    color: Colors.red.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Группировать',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Switch(
                  value: true,
                  onChanged: (value) {},
                  activeColor: Colors.red,
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Показывать только отклонения
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    Icons.warning_outlined,
                    size: 16,
                    color: Colors.red.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Показывать только отклонения',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Switch(
                  value: _showOnlyAbnormal,
                  onChanged: (value) {
                    setState(() {
                      _showOnlyAbnormal = value;
                    });
                    Navigator.pop(context);
                  },
                  activeColor: Colors.red,
                ),
              ],
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
