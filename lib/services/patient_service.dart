import '../models/api_response.dart';
import '../models/patient.dart';
import 'api_client.dart';

/// Сервис для работы с данными пациентов
class PatientService {
  final ApiClient _apiClient;

  PatientService({ApiClient? apiClient})
      : _apiClient = apiClient ?? ApiClient.instance;

  /// Получает информацию о пациенте для авторизованного пользователя
  ///
  /// Возвращает [ApiResponse<Patient>] с данными пациента или ошибкой
  ///
  /// Требует авторизации через Bearer token
  ///
  /// Возможные статусы ответа:
  /// - 'ok' - успешный ответ с данными пациента
  /// - 'user_not_found' - пользователь не найден
  /// - 'no_data' - нет данных пациента для данного пользователя
  Future<ApiResponse<Patient>> getPatient() async {
    print('👤 PatientService.getPatient called');

    try {
      final response = await _apiClient.get('/api/patients');

      print('✅ PatientService: API response received');

      return ApiResponse.fromJson(
        response,
        (data) => Patient.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      print('❌ PatientService error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при получении данных пациента: $e');
    }
  }

  /// Проверяет, существует ли пациент для авторизованного пользователя
  Future<bool> patientExists() async {
    try {
      final response = await getPatient();
      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }

  /// Получает возраст пациента для авторизованного пользователя
  ///
  /// Возвращает null, если пациент не найден или произошла ошибка
  Future<int?> getPatientAge() async {
    try {
      final response = await getPatient();
      return response.isSuccess ? response.data?.age : null;
    } catch (e) {
      return null;
    }
  }

  /// Получает пол пациента для авторизованного пользователя
  ///
  /// Возвращает null, если пациент не найден или произошла ошибка
  Future<Gender?> getPatientGender() async {
    try {
      final response = await getPatient();
      return response.isSuccess ? response.data?.gender : null;
    } catch (e) {
      return null;
    }
  }

  /// Закрывает сервис и освобождает ресурсы
  void dispose() {
    _apiClient.dispose();
  }
}
