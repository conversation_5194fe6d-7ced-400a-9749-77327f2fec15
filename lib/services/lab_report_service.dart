import '../models/api_response.dart';
import '../models/lab_report.dart';
import 'api_client.dart';

/// Сервис для работы с лабораторными отчетами
class LabReportService {
  final ApiClient _apiClient;

  LabReportService({ApiClient? apiClient})
      : _apiClient = apiClient ?? ApiClient.instance;

  /// Получает все лабораторные отчеты для авторизованного пользователя
  ///
  /// Возвращает [ApiResponse<List<LabReport>>] с данными отчетов или ошибкой
  ///
  /// Требует авторизации через Bearer token
  ///
  /// Возможные статусы ответа:
  /// - 'ok' - успешный ответ с данными отчетов
  /// - 'no_data' - нет данных отчетов для данного пользователя
  Future<ApiResponse<List<LabReport>>> getLabReports() async {
    print('📋 LabReportService.getLabReports called');
    print('🔍 LabReportService: ApiClient instance: ${_apiClient.hashCode}');

    try {
      final response = await _apiClient.get('/api/lab-reports');

      print('✅ LabReportService: API response received');

      return ApiResponse.fromJson(
        response,
        (data) {
          if (data is Map<String, dynamic>) {
            // API возвращает объект с полем reports
            final reports = data['reports'];
            if (reports is List) {
              return reports
                  .map((item) => LabReport.fromJson(item as Map<String, dynamic>))
                  .toList();
            }
          } else if (data is List) {
            // Если API вернет массив напрямую
            return data
                .map((item) => LabReport.fromJson(item as Map<String, dynamic>))
                .toList();
          }
          return <LabReport>[];
        },
      );
    } catch (e) {
      print('❌ LabReportService error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при получении лабораторных отчетов: $e');
    }
  }

  /// Получает детальную информацию о конкретном лабораторном отчете
  ///
  /// Возвращает [ApiResponse<LabReportDetail>] с детальными данными отчета и всеми анализами
  ///
  /// Параметры:
  /// - [reportId] - ID лабораторного отчета
  ///
  /// Требует авторизации через Bearer token
  ///
  /// Возможные статусы ответа:
  /// - 'ok' - успешный ответ с данными отчета
  /// - 'no_data' - отчет с данным ID не найден
  Future<ApiResponse<LabReportDetail>> getLabReportDetail(String reportId) async {
    print('📋 LabReportService.getLabReportDetail called');
    print('🔍 LabReportService: Report ID: $reportId');
    print('🔍 LabReportService: ApiClient instance: ${_apiClient.hashCode}');

    try {
      final response = await _apiClient.get('/api/lab-reports/$reportId');

      print('✅ LabReportService: API response received for report detail');

      return ApiResponse.fromJson(
        response,
        (data) => LabReportDetail.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      print('❌ LabReportService getLabReportDetail error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при получении детальной информации об отчете: $e');
    }
  }

  /// Получает отчеты с определенным статусом для авторизованного пользователя
  ///
  /// Параметры:
  /// - [status] - статус отчетов для фильтрации
  Future<List<LabReport>?> getLabReportsByStatus(LabReportStatus status) async {
    try {
      final response = await getLabReports();
      if (response.isSuccess && response.data != null) {
        return response.data!.where((report) => report.status == status).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает только завершенные отчеты для авторизованного пользователя
  Future<List<LabReport>?> getCompletedLabReports() async {
    return getLabReportsByStatus(LabReportStatus.completed);
  }

  /// Получает отчеты, которые находятся в процессе обработки
  Future<List<LabReport>?> getProcessingLabReports() async {
    try {
      final response = await getLabReports();
      if (response.isSuccess && response.data != null) {
        return response.data!.where((report) => report.isProcessing).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает отчеты с ошибками обработки
  Future<List<LabReport>?> getFailedLabReports() async {
    return getLabReportsByStatus(LabReportStatus.failed);
  }

  /// Получает статистику отчетов для авторизованного пользователя
  ///
  /// Возвращает Map с количеством отчетов по статусам
  Future<Map<String, int>?> getLabReportsStatistics() async {
    try {
      final response = await getLabReports();
      if (response.isSuccess && response.data != null) {
        final reports = response.data!;
        return {
          'total': reports.length,
          'completed': reports.where((r) => r.isCompleted).length,
          'processing': reports.where((r) => r.isProcessing).length,
          'failed': reports.where((r) => r.isFailed).length,
        };
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает общее количество анализов из всех завершенных отчетов
  Future<int> getTotalTestsCount() async {
    try {
      final completedReports = await getCompletedLabReports();
      if (completedReports != null) {
        int total = 0;
        for (final report in completedReports) {
          total += report.testsCount;
        }
        return total;
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  /// Проверяет, есть ли у авторизованного пользователя лабораторные отчеты
  Future<bool> hasLabReports() async {
    try {
      final response = await getLabReports();
      return response.isSuccess &&
             response.data != null &&
             response.data!.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Проверяет, есть ли отчеты в процессе обработки
  Future<bool> hasProcessingReports() async {
    try {
      final processingReports = await getProcessingLabReports();
      return processingReports != null && processingReports.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Находит отчет по ID
  Future<LabReport?> getLabReportById(String reportId) async {
    try {
      final response = await getLabReports();
      if (response.isSuccess && response.data != null) {
        return response.data!.firstWhere(
          (report) => report.id == reportId,
          orElse: () => throw StateError('Report not found'),
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает временную ссылку на документ лабораторного отчета
  ///
  /// Параметры:
  /// - [reportId] - ID отчета для получения ссылки на документ
  ///
  /// Возвращает:
  /// - [ApiResponse<String>] с временной ссылкой на документ
  ///
  /// Требует авторизации через Bearer token
  ///
  /// Возможные статусы ответа:
  /// - 'ok' - успешный ответ с временной ссылкой
  /// - 'no_data' - отчет или документ не найден
  Future<ApiResponse<String>> getLabReportDocumentUrl(String reportId) async {
    print('📋 LabReportService.getLabReportDocumentUrl called');
    print('🔍 LabReportService: Report ID: $reportId');
    print('🔍 LabReportService: ApiClient instance: ${_apiClient.hashCode}');

    try {
      final response = await _apiClient.get('/api/lab-reports/$reportId/document-url');

      print('✅ LabReportService: Document URL API response received');

      return ApiResponse.fromJson(
        response,
        (data) {
          if (data is Map<String, dynamic>) {
            // API возвращает ссылку в поле 'url', а не 'documentUrl'
            return data['url']?.toString() ?? '';
          } else if (data is String) {
            return data;
          }
          return '';
        },
      );
    } catch (e) {
      print('❌ LabReportService getLabReportDocumentUrl error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при получении ссылки на документ: $e');
    }
  }

  /// Закрывает сервис и освобождает ресурсы
  void dispose() {
    _apiClient.dispose();
  }
}
