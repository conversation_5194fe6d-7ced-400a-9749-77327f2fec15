import 'dart:io';
import '../models/api_response.dart';
import '../models/lab_test.dart';
import 'api_client.dart';

/// Модель ответа при загрузке файла (соответствует реальному API)
class FileUploadResponse {
  final String status;
  final String message;
  final String filename;
  final String reportId;
  final int size;

  FileUploadResponse({
    required this.status,
    required this.message,
    required this.filename,
    required this.reportId,
    required this.size,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      status: json['status'] as String,
      message: json['message'] as String,
      filename: json['filename'] as String,
      reportId: json['reportId'] as String,
      size: json['size'] as int,
    );
  }
}

/// Сервис для работы с лабораторными анализами
class LabTestService {
  final ApiClient _apiClient;

  LabTestService({ApiClient? apiClient})
      : _apiClient = apiClient ?? ApiClient.instance;

  /// Получает все лабораторные анализы для авторизованного пользователя
  ///
  /// Возвращает [ApiResponse<GroupedLabTests>] с данными анализов или ошибкой
  ///
  /// Требует авторизации через Bearer token
  ///
  /// Возможные статусы ответа:
  /// - 'ok' - успешный ответ с данными анализов
  /// - 'no_data' - нет данных анализов для данного пользователя
  Future<ApiResponse<GroupedLabTests>> getLabTests() async {
    print('🔬 LabTestService.getLabTests called');
    print('🔍 LabTestService: ApiClient instance: ${_apiClient.hashCode}');

    try {
      final response = await _apiClient.get('/api/lab-tests');

      print('✅ LabTestService: API response received');

      return ApiResponse.fromJson(
        response,
        (data) => GroupedLabTests.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      print('❌ LabTestService error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при получении лабораторных анализов: $e');
    }
  }

  /// Получает конкретный лабораторный анализ с историей
  /// 
  /// Возвращает [ApiResponse<LabTestDetail>] с детальными данными анализа
  /// 
  /// Параметры:
  /// - [testId] - ID лабораторного анализа
  /// 
  /// Возможные статусы ответа:
  /// - 'ok' - успешный ответ с данными анализа
  /// - 'no_data' - анализ с данным ID не найден
  Future<ApiResponse<LabTestDetail>> getLabTest(String testId) async {
    try {
      final response = await _apiClient.get('/api/lab-tests/$testId');

      return ApiResponse.fromJson(
        response,
        (data) => LabTestDetail.fromJson(data as Map<String, dynamic>),
      );
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при получении анализа: $e');
    }
  }

  /// Получает статистику анализов для авторизованного пользователя
  ///
  /// Возвращает Map с количеством анализов по статусам
  Future<Map<String, int>?> getLabTestsStatistics() async {
    try {
      final response = await getLabTests();
      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        return {
          'total': data.totalCount,
          'normal': data.normalCount,
          'abnormal': data.abnormalCount,
          'other': data.otherCount,
        };
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает все анализы определенной категории для авторизованного пользователя
  ///
  /// Параметры:
  /// - [category] - название категории (например, "Биохимия крови")
  Future<List<LabTest>?> getLabTestsByCategory(String category) async {
    try {
      final response = await getLabTests();
      if (response.isSuccess && response.data != null) {
        return response.data!.groupLabTests[category];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает все доступные категории анализов для авторизованного пользователя
  Future<List<String>?> getLabTestCategories() async {
    try {
      final response = await getLabTests();
      if (response.isSuccess && response.data != null) {
        return response.data!.groupLabTests.keys.toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Получает анализы с определенным статусом для авторизованного пользователя
  ///
  /// Параметры:
  /// - [status] - статус анализов для фильтрации
  Future<List<LabTest>?> getLabTestsByStatus(LabTestStatus status) async {
    try {
      final response = await getLabTests();
      if (response.isSuccess && response.data != null) {
        final allTests = <LabTest>[];

        // Собираем все анализы из всех категорий
        for (final tests in response.data!.groupLabTests.values) {
          allTests.addAll(tests);
        }

        // Фильтруем по статусу
        return allTests.where((test) => test.status == status).toList();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Проверяет, есть ли у авторизованного пользователя лабораторные анализы
  Future<bool> hasLabTests() async {
    try {
      final response = await getLabTests();
      return response.isSuccess &&
             response.data != null &&
             response.data!.totalCount > 0;
    } catch (e) {
      return false;
    }
  }

  /// Загружает файл с результатами анализов
  ///
  /// Параметры:
  /// - [file] - файл для загрузки (PDF, JPG, PNG)
  /// - [description] - описание файла (необязательно)
  ///
  /// Возвращает [FileUploadResponse] с информацией о загруженном файле
  ///
  /// Требует авторизации через Bearer token
  ///
  /// Возможные ошибки:
  /// - 'INVALID_FILE_FORMAT' - неподдерживаемый формат файла
  /// - 'FILE_TOO_LARGE' - файл слишком большой
  /// - 'PROCESSING_ERROR' - ошибка обработки файла
  Future<FileUploadResponse> uploadLabTestFile(
    File file, {
    String? description,
  }) async {
    print('📤 LabTestService.uploadLabTestFile called');
    print('   File: ${file.path}');
    print('   Size: ${await file.length()} bytes');
    if (description != null) {
      print('   Description: $description');
    }

    try {
      // Проверяем, что файл существует
      if (!await file.exists()) {
        throw Exception('Файл не найден: ${file.path}');
      }

      // Проверяем размер файла (максимум 10 МБ)
      final fileSize = await file.length();
      const maxFileSize = 10 * 1024 * 1024; // 10 MB
      if (fileSize > maxFileSize) {
        throw Exception('Файл слишком большой. Максимальный размер: 10 МБ');
      }

      // Проверяем формат файла
      final fileName = file.path.split('/').last.toLowerCase();
      final allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
      final fileExtension = fileName.split('.').last;

      if (!allowedExtensions.contains(fileExtension)) {
        throw Exception('Неподдерживаемый формат файла. Разрешены: ${allowedExtensions.join(', ')}');
      }

      print('✅ LabTestService: Файл прошел валидацию');

      // Выполняем загрузку файла через API клиент
      final response = await _apiClient.uploadFile(
        '/api/lab-reports/upload',
        file: file,
        description: description,
      );

      print('✅ LabTestService: Файл успешно загружен');

      return FileUploadResponse.fromJson(response);
    } catch (e) {
      print('❌ LabTestService upload error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при загрузке файла: $e');
    }
  }

  /// Закрывает сервис и освобождает ресурсы
  void dispose() {
    _apiClient.dispose();
  }
}
