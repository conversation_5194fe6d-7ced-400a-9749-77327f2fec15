import 'package:shared_preferences/shared_preferences.dart';

/// Сервис для работы с постоянным хранилищем данных
class StorageService {
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _accessTokenExpiresAtKey = 'access_token_expires_at';
  static const String _refreshTokenExpiresAtKey = 'refresh_token_expires_at';

  static SharedPreferences? _prefs;

  /// Инициализация SharedPreferences
  static Future<void> init() async {
    print('🗄️ StorageService: Инициализация...');
    try {
      _prefs = await SharedPreferences.getInstance();
      print('✅ StorageService: Инициализирован');
    } catch (e) {
      print('❌ StorageService: Ошибка инициализации: $e');
      print('⚠️ StorageService: Работаем без постоянного хранилища (симулятор)');
      // В случае ошибки создаем заглушку, чтобы приложение не крашилось
    }
  }

  /// Получение экземпляра SharedPreferences
  static SharedPreferences? get _preferences {
    if (_prefs == null) {
      print('⚠️ StorageService: SharedPreferences не доступен');
      return null;
    }
    return _prefs!;
  }

  /// Сохранение токенов авторизации
  static Future<void> saveAuthTokens({
    required String accessToken,
    required String refreshToken,
    required String userId,
    required DateTime accessTokenExpiresAt,
    required DateTime refreshTokenExpiresAt,
  }) async {
    print('💾 StorageService: Сохраняем токены авторизации');
    print('   Access Token: ${accessToken.substring(0, 20)}...');
    print('   Refresh Token: ${refreshToken.substring(0, 20)}...');
    print('   User ID: $userId');
    print('   Access Token истекает: $accessTokenExpiresAt');
    print('   Refresh Token истекает: $refreshTokenExpiresAt');

    final prefs = _preferences;
    if (prefs == null) {
      print('⚠️ StorageService: Не удалось сохранить токены - SharedPreferences недоступен');
      return;
    }

    try {
      await Future.wait([
        prefs.setString(_accessTokenKey, accessToken),
        prefs.setString(_refreshTokenKey, refreshToken),
        prefs.setString(_userIdKey, userId),
        prefs.setInt(_accessTokenExpiresAtKey, accessTokenExpiresAt.millisecondsSinceEpoch),
        prefs.setInt(_refreshTokenExpiresAtKey, refreshTokenExpiresAt.millisecondsSinceEpoch),
      ]);

      print('✅ StorageService: Токены сохранены');
    } catch (e) {
      print('❌ StorageService: Ошибка сохранения токенов: $e');
    }
  }

  /// Получение access токена
  static String? getAccessToken() {
    print('📖 StorageService.getAccessToken: Начинаем чтение access токена');
    final prefs = _preferences;
    if (prefs == null) {
      print('❌ StorageService.getAccessToken: SharedPreferences недоступен');
      return null;
    }

    print('📖 StorageService.getAccessToken: Читаем ключ "$_accessTokenKey"');
    final token = prefs.getString(_accessTokenKey);
    if (token != null) {
      print('✅ StorageService.getAccessToken: Токен найден: ${token.substring(0, 20)}...');
    } else {
      print('❌ StorageService.getAccessToken: Токен НЕ найден');
    }
    return token;
  }

  /// Получение refresh токена
  static String? getRefreshToken() {
    print('📖 StorageService.getRefreshToken: Начинаем чтение refresh токена');
    final prefs = _preferences;
    if (prefs == null) {
      print('❌ StorageService.getRefreshToken: SharedPreferences недоступен');
      return null;
    }

    print('📖 StorageService.getRefreshToken: Читаем ключ "$_refreshTokenKey"');
    final token = prefs.getString(_refreshTokenKey);
    if (token != null) {
      print('✅ StorageService.getRefreshToken: Токен найден: ${token.substring(0, 20)}...');
    } else {
      print('❌ StorageService.getRefreshToken: Токен НЕ найден');
    }
    return token;
  }

  /// Получение ID пользователя
  static String? getUserId() {
    final prefs = _preferences;
    if (prefs == null) {
      print('🔍 StorageService: User ID: НЕТ (SharedPreferences недоступен)');
      return null;
    }

    final userId = prefs.getString(_userIdKey);
    print('🔍 StorageService: User ID: ${userId ?? "НЕТ"}');
    return userId;
  }

  /// Получение времени истечения access токена
  static DateTime? getAccessTokenExpiresAt() {
    print('📖 StorageService.getAccessTokenExpiresAt: Начинаем чтение времени истечения access токена');
    final prefs = _preferences;
    if (prefs == null) {
      print('❌ StorageService.getAccessTokenExpiresAt: SharedPreferences недоступен');
      return null;
    }

    print('📖 StorageService.getAccessTokenExpiresAt: Читаем ключ "$_accessTokenExpiresAtKey"');
    final timestamp = prefs.getInt(_accessTokenExpiresAtKey);
    if (timestamp == null) {
      print('❌ StorageService.getAccessTokenExpiresAt: Timestamp НЕ найден');
      return null;
    }
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    print('✅ StorageService.getAccessTokenExpiresAt: Время найдено: $dateTime (timestamp: $timestamp)');
    return dateTime;
  }

  /// Получение времени истечения refresh токена
  static DateTime? getRefreshTokenExpiresAt() {
    print('📖 StorageService.getRefreshTokenExpiresAt: Начинаем чтение времени истечения refresh токена');
    final prefs = _preferences;
    if (prefs == null) {
      print('❌ StorageService.getRefreshTokenExpiresAt: SharedPreferences недоступен');
      return null;
    }

    print('📖 StorageService.getRefreshTokenExpiresAt: Читаем ключ "$_refreshTokenExpiresAtKey"');
    final timestamp = prefs.getInt(_refreshTokenExpiresAtKey);
    if (timestamp == null) {
      print('❌ StorageService.getRefreshTokenExpiresAt: Timestamp НЕ найден');
      return null;
    }
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    print('✅ StorageService.getRefreshTokenExpiresAt: Время найдено: $dateTime (timestamp: $timestamp)');
    return dateTime;
  }

  /// Обновление access токена
  static Future<void> updateAccessToken({
    required String accessToken,
    required DateTime accessTokenExpiresAt,
  }) async {
    print('🔄 StorageService: Обновляем access токен');
    print('   Новый Access Token: ${accessToken.substring(0, 20)}...');
    print('   Новое время истечения: $accessTokenExpiresAt');

    final prefs = _preferences;
    if (prefs == null) {
      print('⚠️ StorageService: Не удалось обновить токен - SharedPreferences недоступен');
      return;
    }

    try {
      await Future.wait([
        prefs.setString(_accessTokenKey, accessToken),
        prefs.setInt(_accessTokenExpiresAtKey, accessTokenExpiresAt.millisecondsSinceEpoch),
      ]);

      print('✅ StorageService: Access токен обновлен');
    } catch (e) {
      print('❌ StorageService: Ошибка обновления токена: $e');
    }
  }

  /// Очистка всех токенов
  static Future<void> clearAuthTokens() async {
    print('🗑️ StorageService: Очищаем все токены');

    final prefs = _preferences;
    if (prefs == null) {
      print('⚠️ StorageService: Не удалось очистить токены - SharedPreferences недоступен');
      return;
    }

    try {
      await Future.wait([
        prefs.remove(_accessTokenKey),
        prefs.remove(_refreshTokenKey),
        prefs.remove(_userIdKey),
        prefs.remove(_accessTokenExpiresAtKey),
        prefs.remove(_refreshTokenExpiresAtKey),
      ]);

      print('✅ StorageService: Все токены очищены');
    } catch (e) {
      print('❌ StorageService: Ошибка очистки токенов: $e');
    }
  }

  /// Проверка наличия сохраненных токенов
  static bool hasAuthTokens() {
    print('🔍 StorageService.hasAuthTokens: Проверяем наличие сохраненных токенов');
    final prefs = _preferences;
    if (prefs == null) {
      print('❌ StorageService.hasAuthTokens: SharedPreferences недоступен');
      return false;
    }

    final hasAccessToken = prefs.containsKey(_accessTokenKey);
    final hasRefreshToken = prefs.containsKey(_refreshTokenKey);
    final hasUserId = prefs.containsKey(_userIdKey);

    print('🔍 StorageService.hasAuthTokens: Проверка ключей:');
    print('   - $_accessTokenKey: $hasAccessToken');
    print('   - $_refreshTokenKey: $hasRefreshToken');
    print('   - $_userIdKey: $hasUserId');

    final hasTokens = hasAccessToken && hasRefreshToken && hasUserId;
    print('✅ StorageService.hasAuthTokens: Результат: $hasTokens');
    return hasTokens;
  }
}
