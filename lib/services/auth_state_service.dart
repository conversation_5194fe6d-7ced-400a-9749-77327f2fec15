import 'package:flutter/foundation.dart';

/// Сервис для управления состоянием авторизации в приложении
class AuthStateService extends ChangeNotifier {
  static final AuthStateService _instance = AuthStateService._internal();
  factory AuthStateService() => _instance;
  AuthStateService._internal();

  bool _isAuthenticated = false;

  /// Текущий статус авторизации
  bool get isAuthenticated => _isAuthenticated;

  /// Устанавливает статус авторизации
  void setAuthenticationStatus(bool isAuthenticated) {
    if (_isAuthenticated != isAuthenticated) {
      _isAuthenticated = isAuthenticated;
      print('🔐 AuthStateService: Статус авторизации изменен: $isAuthenticated');
      notifyListeners();
    }
  }

  /// Выполняет выход из системы
  void logout() {
    print('🚪 AuthStateService: Выполняем выход из системы');
    setAuthenticationStatus(false);
  }

  /// Выполняет вход в систему
  void login() {
    print('✅ AuthStateService: Выполняем вход в систему');
    setAuthenticationStatus(true);
  }
}
