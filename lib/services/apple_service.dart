import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'auth_service.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';


/// Сервис для работы с Apple Sign In
class AppleService {
  static AppleService? _instance;
  bool _isInitialized = false;

  AppleService._();

  /// Singleton instance
  static AppleService get instance {
    _instance ??= AppleService._();
    return _instance!;
  }

  /// Проверяет, доступен ли Apple Sign In
  bool get isAvailable {
    // Apple Sign In доступен только на iOS 13+ и macOS 10.15+
    if (defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.macOS) {
      return true;
    }
    return false;
  }

  /// Проверяет, инициализирован ли сервис
  bool get isInitialized => _isInitialized;

  /// Инициализирует Apple Sign In
  Future<bool> initialize() async {
    if (!isAvailable) {
      print('❌ AppleService: Apple Sign In недоступен на этой платформе');
      return false;
    }

    try {
      print('🍎 AppleService: Инициализация Apple Sign In');
      _isInitialized = true;
      print('✅ AppleService: Инициализирован');
      return true;
    } catch (e) {
      print('❌ AppleService initialization error: $e');
      return false;
    }
  }

  /// Выполняет авторизацию через Apple Sign In
  Future<AppleLoginRequest?> signIn() async {
    if (!_isInitialized) {
      print('❌ AppleService: Сервис не инициализирован');
      return null;
    }

    try {
      print('🍎 AppleService: Начинаем Apple Sign In через библиотеку');

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      print('✅ AppleService: Apple Sign In успешен');

      return AppleLoginRequest(
        identityToken: credential.identityToken,
        authorizationCode: credential.authorizationCode,
        email: credential.email,
        firstName: credential.givenName,
        lastName: credential.familyName,
        userIdentifier: credential.userIdentifier,
      );
    } catch (e) {
      print('❌ AppleService: Ошибка Apple Sign In: $e');
      return null;
    }
  }

  /// Создает тестовый JWT токен для разработки
  String _createTestJWT() {
    // Простой тестовый JWT токен (в реальном приложении это будет настоящий токен от Apple)
    // Header
    final header = {
      'alg': 'RS256',
      'typ': 'JWT',
      'kid': 'test_key_id'
    };
    
    // Payload
    final payload = {
      'iss': 'https://appleid.apple.com',
      'aud': 'com.example.medstata',
      'exp': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'sub': 'test_user_identifier',
      'email': '<EMAIL>',
      'email_verified': 'true',
    };
    
    // В реальном приложении здесь будет правильное кодирование и подпись
    // Для тестирования используем простую строку
    return 'test_jwt_header.test_jwt_payload.test_jwt_signature';
  }

  /// Проверяет статус авторизации Apple Sign In
  Future<bool> checkAuthorizationStatus() async {
    if (!_isInitialized) {
      return false;
    }

    try {
      const platform = MethodChannel('apple_signin');
      final result = await platform.invokeMethod('getAuthorizationStatus');
      return result as bool? ?? false;
    } catch (e) {
      print('❌ AppleService: Ошибка проверки статуса авторизации: $e');
      return false;
    }
  }

  /// Выполняет выход из Apple Sign In
  Future<void> signOut() async {
    try {
      print('🍎 AppleService: Выход из Apple Sign In');
      // Apple Sign In не требует явного выхода, но можно очистить локальные данные
    } catch (e) {
      print('❌ AppleService sign out error: $e');
    }
  }

  /// Получает информацию для отладки
  Map<String, dynamic> getDebugInfo() {
    return {
      'initialized': _isInitialized,
      'available': isAvailable,
      'platform': defaultTargetPlatform.toString(),
    };
  }

  /// Симулирует Apple Sign In для тестирования
  Future<AppleLoginRequest?> simulateAppleSignIn({
    String? email,
    String? firstName,
    String? lastName,
  }) async {
    print('🧪 AppleService: Симуляция Apple Sign In');
    
    final testRequest = AppleLoginRequest(
      identityToken: _createTestJWT(),
      authorizationCode: 'simulated_authorization_code',
      email: email ?? '<EMAIL>',
      firstName: firstName ?? 'Simulated',
      lastName: lastName ?? 'User',
      userIdentifier: 'simulated_user_identifier',
    );
    
    print('✅ AppleService: Симуляция завершена');
    print('   Email: ${testRequest.email}');
    print('   Name: ${testRequest.firstName} ${testRequest.lastName}');
    
    return testRequest;
  }
}
