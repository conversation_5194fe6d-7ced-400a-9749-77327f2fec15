import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

import '../config/app_config.dart';

/// Исключения API
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, [this.statusCode]);

  @override
  String toString() => 'ApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
}

/// HTTP клиент для работы с MedStata API
class ApiClient {
  static ApiClient? _instance;

  final String baseUrl;
  http.Client _httpClient;
  final Duration timeout;
  String? _accessToken;

  ApiClient._({
    String? baseUrl,
    http.Client? httpClient,
    this.timeout = const Duration(seconds: 30),
  }) : baseUrl = baseUrl ?? _getDefaultBaseUrl(),
       _httpClient = httpClient ?? http.Client();

  /// Singleton instance
  static ApiClient get instance {
    _instance ??= ApiClient._();
    return _instance!;
  }

  /// Создает новый экземпляр (для тестирования)
  factory ApiClient({
    String? baseUrl,
    http.Client? httpClient,
    Duration timeout = const Duration(seconds: 30),
  }) {
    return ApiClient._(
      baseUrl: baseUrl,
      httpClient: httpClient,
      timeout: timeout,
    );
  }

  /// Получает рабочий HTTP клиент, создает новый если нужно
  http.Client _getHttpClient() {
    // Всегда создаем новый клиент для каждого запроса
    // Это решает проблему с закрытыми клиентами после hot reload
    return http.Client();
  }

  /// Устанавливает access токен для авторизации
  void setAccessToken(String? token) {
    _accessToken = token;
    if (token != null) {
      print('🔑 ApiClient(${hashCode}): Access токен установлен: ${token.substring(0, 20)}...');
    } else {
      print('🔑 ApiClient(${hashCode}): Access токен очищен');
    }
  }

  /// Callback для обновления токена
  Future<void> Function()? _refreshTokenCallback;

  /// Callback для проверки валидности токена
  bool Function()? _isTokenValidCallback;

  /// Callback для проверки наличия refresh токена
  bool Function()? _hasRefreshTokenCallback;

  /// Callback для выхода из системы (перенаправление на экран авторизации)
  void Function()? _logoutCallback;

  /// Устанавливает callback для обновления токена
  void setRefreshTokenCallback(Future<void> Function()? callback) {
    _refreshTokenCallback = callback;
  }

  /// Устанавливает callback для проверки валидности токена
  void setTokenValidationCallback(bool Function()? callback) {
    _isTokenValidCallback = callback;
  }

  /// Устанавливает callback для проверки наличия refresh токена
  void setRefreshTokenCheckCallback(bool Function()? callback) {
    _hasRefreshTokenCallback = callback;
  }

  /// Устанавливает callback для выхода из системы
  void setLogoutCallback(void Function()? callback) {
    _logoutCallback = callback;
  }

  /// Проверяет, нужно ли обновить токен на основе ответа сервера
  bool _shouldRefreshToken(int statusCode) {
    return statusCode == 401 || statusCode == 403;
  }

  /// Проверяет и обновляет токен если нужно перед запросом
  Future<void> _ensureValidToken() async {
    print('🔍 ApiClient: Проверяем валидность токена...');

    if (_accessToken == null) {
      print('❌ ApiClient: Access токен отсутствует - прерываем запрос');

      // Проверяем наличие refresh токена
      if (_hasRefreshTokenCallback != null) {
        final hasRefreshToken = _hasRefreshTokenCallback!();
        print('🔍 ApiClient: Refresh токен ${hasRefreshToken ? "ЕСТЬ" : "ОТСУТСТВУЕТ"}');

        if (hasRefreshToken && _refreshTokenCallback != null) {
          print('🔄 ApiClient: Пытаемся обновить токен перед запросом...');
          try {
            await _refreshTokenCallback!();
            print('✅ ApiClient: Токен обновлен, продолжаем запрос');
            return; // Токен обновлен, можно продолжать
          } catch (e) {
            print('❌ ApiClient: Не удалось обновить токен: $e');
          }
        }
      } else {
        print('❌ ApiClient: Callback проверки refresh токена не установлен');
      }

      // Если нет токенов - перенаправляем на экран авторизации
      print('🚪 ApiClient: Нет токенов - перенаправляем на экран авторизации');
      if (_logoutCallback != null) {
        print('🔄 ApiClient: Вызываем logout callback');
        _logoutCallback!();
        print('✅ ApiClient: Logout callback выполнен');
      } else {
        print('❌ ApiClient: Logout callback не установлен');
      }

      throw ApiException('Access токен отсутствует. Требуется авторизация.');
    }

    if (_isTokenValidCallback == null) {
      print('⚠️ ApiClient: Callback проверки валидности не установлен');
      return;
    }

    final isValid = _isTokenValidCallback!();
    print('🔍 ApiClient: Токен валиден: $isValid');

    if (!isValid) {
      print('⏰ ApiClient: Access токен истек, обновляем...');
      if (_refreshTokenCallback != null) {
        try {
          await _refreshTokenCallback!();
          print('✅ ApiClient: Токен успешно обновлен');
        } catch (e) {
          print('❌ ApiClient: Не удалось обновить токен: $e');
          throw ApiException('Не удалось обновить токен: $e');
        }
      } else {
        print('❌ ApiClient: Callback обновления токена не установлен');
        throw ApiException('Система обновления токенов не настроена');
      }
    }
  }

  /// Получает базовый URL в зависимости от платформы
  static String _getDefaultBaseUrl() {
    return AppConfig.baseUrl;
  }

  /// Выполняет GET запрос с автоматическим обновлением токена
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? queryParameters,
    Map<String, String>? headers,
    bool isRetry = false,
  }) async {
    // Проверяем и обновляем токен если нужно (только для первого запроса)
    if (!isRetry) {
      try {
        await _ensureValidToken();
      } catch (e) {
        print('❌ ApiClient: Прерываем GET запрос: $e');
        throw e;
      }
    }

    final uri = _buildUri(endpoint, queryParameters);

    // Логирование запроса
    print('🌐 API Request: GET $uri');
    if (queryParameters != null) {
      print('📋 Query Parameters: $queryParameters');
    }

    try {
      final response = await _getHttpClient()
          .get(uri, headers: _buildHeaders(headers))
          .timeout(timeout);

      print('✅ API Response: ${response.statusCode}');
      print('📄 Response Body: ${response.body}');

      // Проверяем, нужно ли обновить токен
      if (_shouldRefreshToken(response.statusCode) && !isRetry && _refreshTokenCallback != null) {
        print('🔄 ApiClient: Получен 401, обновляем токен...');

        try {
          await _refreshTokenCallback!();

          // Повторяем запрос с новым токеном
          print('🔄 ApiClient: Повторяем GET запрос с новым токеном');
          return await get(endpoint, queryParameters: queryParameters, headers: headers, isRetry: true);

        } catch (refreshError) {
          print('❌ ApiClient: Не удалось обновить токен: $refreshError');
          // Возвращаем оригинальный ответ
        }
      }

      return _handleResponse(response);
    } on SocketException catch (e) {
      print('❌ Socket Exception: $e');
      throw ApiException('Нет подключения к интернету');
    } on HttpException catch (e) {
      print('❌ HTTP Exception: $e');
      throw ApiException('HTTP ошибка: ${e.message}');
    } on FormatException catch (e) {
      print('❌ Format Exception: $e');
      throw ApiException('Неверный формат ответа от сервера');
    } catch (e) {
      print('❌ Unknown Exception: $e');
      throw ApiException('Неизвестная ошибка: $e');
    }
  }

  /// Выполняет POST запрос с автоматическим обновлением токена
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParameters,
    Map<String, String>? headers,
    bool isRetry = false,
  }) async {
    // Проверяем и обновляем токен если нужно (только для первого запроса и не для auth endpoints)
    final isAuthEndpoint = endpoint.contains('/api/auth/');
    if (!isRetry && !isAuthEndpoint) {
      try {
        await _ensureValidToken();
      } catch (e) {
        print('❌ ApiClient: Прерываем POST запрос: $e');
        throw e;
      }
    }

    final uri = _buildUri(endpoint, queryParameters);

    // Логирование запроса
    print('🌐 API Request: POST $uri');
    if (queryParameters != null) {
      print('📋 Query Parameters: $queryParameters');
    }
    if (body != null) {
      print('📄 Request Body: $body');
    }

    try {
      final response = await _getHttpClient()
          .post(
            uri,
            headers: _buildHeaders(headers),
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(timeout);

      print('✅ API Response: ${response.statusCode}');
      print('📄 Response Body: ${response.body}');

      // Проверяем, нужно ли обновить токен (но не для auth endpoints)
      final isAuthEndpoint = endpoint.contains('/api/auth/');
      if (_shouldRefreshToken(response.statusCode) && !isRetry && !isAuthEndpoint && _refreshTokenCallback != null) {
        print('🔄 ApiClient: Получен 401, обновляем токен...');

        try {
          await _refreshTokenCallback!();

          // Повторяем запрос с новым токеном
          print('🔄 ApiClient: Повторяем POST запрос с новым токеном');
          return await post(endpoint, body: body, queryParameters: queryParameters, headers: headers, isRetry: true);

        } catch (refreshError) {
          print('❌ ApiClient: Не удалось обновить токен: $refreshError');
          // Возвращаем оригинальный ответ
        }
      }

      return _handleResponse(response);
    } on SocketException catch (e) {
      print('❌ Socket Exception: $e');
      throw ApiException('Нет подключения к интернету');
    } on HttpException catch (e) {
      print('❌ HTTP Exception: $e');
      throw ApiException('HTTP ошибка: ${e.message}');
    } on FormatException catch (e) {
      print('❌ Format Exception: $e');
      throw ApiException('Неверный формат ответа от сервера');
    } catch (e) {
      print('❌ Unknown Exception: $e');
      throw ApiException('Неизвестная ошибка: $e');
    }
  }

  /// Строит URI с параметрами запроса
  Uri _buildUri(String endpoint, [Map<String, String>? queryParameters]) {
    final uri = Uri.parse('$baseUrl$endpoint');
    
    if (queryParameters != null && queryParameters.isNotEmpty) {
      return uri.replace(queryParameters: queryParameters);
    }
    
    return uri;
  }

  /// Строит заголовки запроса
  Map<String, String> _buildHeaders(Map<String, String>? customHeaders) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-API-Key': AppConfig.apiKey,
    };

    print('🔑 ApiClient(${hashCode}): Добавлен X-API-Key: ${AppConfig.apiKey}');

    // Добавляем авторизацию если токен установлен
    if (_accessToken != null) {
      headers['Authorization'] = 'Bearer $_accessToken';
      print('🔑 ApiClient(${hashCode}): Добавлен Bearer токен: ${_accessToken!.substring(0, 20)}...');
    } else {
      print('⚠️ ApiClient(${hashCode}): Access токен не установлен');
    }

    if (customHeaders != null) {
      headers.addAll(customHeaders);
    }

    return headers;
  }

  /// Обрабатывает ответ от сервера
  Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        throw ApiException('Не удалось декодировать ответ сервера');
      }
    } else {
      String errorMessage = 'HTTP ${response.statusCode}';
      
      try {
        final errorBody = jsonDecode(response.body) as Map<String, dynamic>;
        errorMessage = errorBody['message'] ?? errorMessage;
      } catch (e) {
        // Игнорируем ошибки декодирования для error body
      }
      
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// Загружает файл на сервер
  Future<Map<String, dynamic>> uploadFile(
    String endpoint, {
    required File file,
    String? description,
    Map<String, String>? additionalFields,
    Map<String, String>? headers,
    bool isRetry = false,
  }) async {
    // Проверяем и обновляем токен если нужно (только для первого запроса)
    if (!isRetry) {
      try {
        await _ensureValidToken();
      } catch (e) {
        print('❌ ApiClient: Прерываем UPLOAD запрос: $e');
        throw e;
      }
    }

    final uri = _buildUri(endpoint, null);

    // Логирование запроса
    print('🌐 API Request: POST $uri (file upload)');
    print('📁 File: ${file.path}');
    print('📏 File size: ${await file.length()} bytes');

    try {
      // Создаем multipart request
      final request = http.MultipartRequest('POST', uri);

      // Добавляем заголовки авторизации
      if (_accessToken != null) {
        request.headers['Authorization'] = 'Bearer $_accessToken';
        print('🔑 ApiClient: Добавлен Bearer токен для upload');
      }

      // Добавляем X-API-Key заголовок
      request.headers['X-API-Key'] = AppConfig.apiKey;
      print('🔑 ApiClient: Добавлен X-API-Key для upload: ${AppConfig.apiKey}');

      // Добавляем дополнительные заголовки
      if (headers != null) {
        request.headers.addAll(headers);
      }

      // Добавляем файл
      final fileName = file.path.split('/').last;
      request.files.add(
        await http.MultipartFile.fromPath(
          'file',
          file.path,
          filename: fileName,
        ),
      );

      // Добавляем описание если есть
      if (description != null) {
        request.fields['description'] = description;
      }

      // Добавляем дополнительные поля если есть
      if (additionalFields != null) {
        request.fields.addAll(additionalFields);
      }

      // Отправляем запрос
      final streamedResponse = await _getHttpClient().send(request).timeout(timeout);
      final response = await http.Response.fromStream(streamedResponse);

      print('✅ API Response: ${response.statusCode}');
      print('📄 Response Body: ${response.body}');

      // Проверяем, нужно ли обновить токен
      if (_shouldRefreshToken(response.statusCode) && !isRetry && _refreshTokenCallback != null) {
        print('🔄 ApiClient: Получен 401, обновляем токен...');

        try {
          await _refreshTokenCallback!();

          // Повторяем запрос с новым токеном
          print('🔄 ApiClient: Повторяем UPLOAD запрос с новым токеном');
          return await uploadFile(
            endpoint,
            file: file,
            description: description,
            additionalFields: additionalFields,
            headers: headers,
            isRetry: true,
          );

        } catch (refreshError) {
          print('❌ ApiClient: Не удалось обновить токен: $refreshError');
          // Возвращаем оригинальный ответ
        }
      }

      return _handleResponse(response);
    } on SocketException catch (e) {
      print('❌ Socket Exception: $e');
      throw ApiException('Нет подключения к интернету');
    } on HttpException catch (e) {
      print('❌ HTTP Exception: $e');
      throw ApiException('HTTP ошибка: ${e.message}');
    } on FormatException catch (e) {
      print('❌ Format Exception: $e');
      throw ApiException('Неверный формат ответа от сервера');
    } catch (e) {
      print('❌ Unknown Exception: $e');
      throw ApiException('Неизвестная ошибка: $e');
    }
  }

  /// Закрывает HTTP клиент
  void dispose() {
    _httpClient.close();
  }
}
