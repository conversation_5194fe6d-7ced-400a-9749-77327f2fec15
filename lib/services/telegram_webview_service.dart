import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../config/app_config.dart';
import 'auth_service.dart';

/// Сервис для авторизации через Telegram WebView
class TelegramWebViewService {
  /// Динамически формируем Telegram OAuth URL в зависимости от окружения
  static String get telegramAuthUrl => AppConfig.telegramOAuthUrl;

  /// Открывает WebView для авторизации через Telegram
  static Future<AuthResponse?> authenticateWithTelegram(BuildContext context) async {
    return await Navigator.of(context).push<AuthResponse>(
      MaterialPageRoute(
        builder: (context) => const TelegramWebViewScreen(),
        fullscreenDialog: true,
      ),
    );
  }
}

/// Экран с WebView для авторизации через Telegram
class TelegramWebViewScreen extends StatefulWidget {
  const TelegramWebViewScreen({super.key});

  @override
  State<TelegramWebViewScreen> createState() => _TelegramWebViewScreenState();
}

class _TelegramWebViewScreenState extends State<TelegramWebViewScreen> {
  bool _authHandled = false;
  late final WebViewController _controller;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)

      ..addJavaScriptChannel(
        'telegramAuth',
        onMessageReceived: (JavaScriptMessage message) async {
          if (_authHandled) return;
          _authHandled = true;

          print('📨 Получено сообщение от Telegram: ${message.message}');
          try {
            final Map<String, dynamic> data = jsonDecode(message.message);
            if (data.containsKey('id') && data.containsKey('hash')) {
              final auth = TelegramLoginRequest(
                id: data['id'],
                first_name: data['first_name'],
                last_name: data['last_name'],
                username: data['username'],
                photo_url: data['photo_url'],
                auth_date: data['auth_date'],
                hash: data['hash'],
              );

              final authService = AuthService();
              final result = await authService.telegramLogin(auth);

              // Даем небольшую задержку для завершения всех операций авторизации
              await Future.delayed(const Duration(milliseconds: 100));

              if (mounted && Navigator.of(context).canPop()) {
                print('📦 Закрываем WebView и возвращаем результат: $result');
                Navigator.of(context).pop(result);
              } else {
                print('⚠️ WebView уже закрыт или контекст недоступен - это нормально при быстром переходе между экранами');
                // Не показываем это как ошибку, так как это может быть нормальным поведением
                // когда AuthScreen быстро переходит к HomeScreen
              }
            } else {
              throw Exception('Данные авторизации невалидны');
            }
          } catch (e) {
            print('❌ Ошибка разбора postMessage: $e');
            if (mounted) {
              setState(() => _errorMessage = 'Ошибка авторизации: $e');
            }
          }
        },
      )

      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            print('🌐 TelegramWebView: Загрузка страницы: $url');
            setState(() {
              _isLoading = true;
              _errorMessage = null;
            });
          },
          onPageFinished: (String url) {
            print('✅ TelegramWebView: Страница загружена: $url');
            setState(() {
              _isLoading = false;
            });

            Uri uri = Uri.parse(url);
            if (uri.host.contains('medstata.avpuser.ru') &&
                uri.path.contains('telegram-auth-complete.html')) {
              print('✅ Реальный переход на telegram-auth-complete.html — начинаем обработку');
              _setupPostMessageListener();
              // _checkForAuthResult(url);
            }
          },
          onWebResourceError: (WebResourceError error) {
            print('❌ TelegramWebView: Ошибка загрузки: ${error.description}');
            setState(() {
              _isLoading = false;
              _errorMessage = 'Ошибка загрузки: ${error.description}';
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            print('🔄 TelegramWebView: Навигация на: ${request.url}');
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(TelegramWebViewService.telegramAuthUrl));
  }

  /// Настраивает обработчик postMessage событий
  void _setupPostMessageListener() {
    _controller.runJavaScript('''
      try {
        // Обработчик для postMessage событий
        window.addEventListener('message', function(event) {
          try {
            if (event.data && typeof event.data === 'object') {
              // Сохраняем данные в глобальную переменную
              window.telegramAuthResult = event.data;

              // Также сохраняем в localStorage для надежности (если доступен)
              if (typeof localStorage !== 'undefined') {
                localStorage.setItem('telegram_auth_data', JSON.stringify(event.data));
              }
            }
          } catch (e) {
            // Игнорируем ошибки обработки postMessage
          }
        });

        // Проверяем, есть ли уже данные в Telegram WebApp
        if (typeof window.Telegram !== 'undefined' && window.Telegram.WebApp) {
          // Telegram WebApp доступен
        }
      } catch (e) {
        // Игнорируем ошибки настройки
      }
    ''');
  }

  void _checkForAuthResult(String url) {
    print('🔍 TelegramWebView: Проверка URL на результат авторизации: $url');

    // Проверяем, редиректнуло ли на страницу telegram-auth-complete.html
    if (url.contains('telegram-auth-complete.html')) {
      print('✅ TelegramWebView: Мы на странице telegram-auth-complete.html — начинаем извлечение данных');
      _handleAuthCallback(url);
    } else {
      print('ℹ️ TelegramWebView: Пока не на telegram-auth-complete.html, ждём дальше');
    }
  }

  /// Обрабатывает callback от Telegram авторизации
  void _handleAuthCallback(String url) async {
    if (_authHandled) return; // Предотвращаем повторную обработку

    print('🔐 TelegramWebView: Обработка callback: $url');

    try {
      _authHandled = true; // Устанавливаем флаг для предотвращения повторной обработки

      // Ждем немного, чтобы страница полностью загрузилась и выполнился JavaScript
      //await Future.delayed(const Duration(milliseconds: 2000));

      // Извлекаем данные авторизации из страницы
      final authData = await _extractAuthDataFromPage();

      if (authData != null && authData['id'] != null && authData['hash'] != null) {
        print('✅ TelegramWebView: Получены данные авторизации:');
        print('   ID: ${authData['id']}');
        print('   Имя: ${authData['first_name']} ${authData['last_name']}');
        print('   Username: ${authData['username']}');
        print('   Auth Date: ${authData['auth_date']}');
        print('   Hash: ${authData['hash']?.toString().substring(0, 10)}...');

        // Создаем запрос для авторизации
        final loginRequest = TelegramLoginRequest(
          id: authData['id'] as int,
          first_name: authData['first_name'] as String?,
          last_name: authData['last_name'] as String?,
          username: authData['username'] as String?,
          photo_url: authData['photo_url'] as String?,
          auth_date: authData['auth_date'] as int,
          hash: authData['hash'] as String,
        );

        // Выполняем авторизацию на сервере
        final authService = AuthService();
        final authResponse = await authService.telegramLogin(loginRequest);

        // Возвращаем результат
        if (mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop(authResponse);
        }
      } else {
        // Fallback: пробуем извлечь данные напрямую из URL
        print('⚠️ TelegramWebView: Не удалось извлечь данные со страницы, пробуем URL');
        final urlAuthData = await _extractAuthDataFromUrl(url);

        if (urlAuthData != null && urlAuthData['id'] != null && urlAuthData['hash'] != null) {
          print('✅ TelegramWebView: Получены данные авторизации из URL');

          // Создаем запрос для авторизации
          final loginRequest = TelegramLoginRequest(
            id: urlAuthData['id'] as int,
            first_name: urlAuthData['first_name'] as String?,
            last_name: urlAuthData['last_name'] as String?,
            username: urlAuthData['username'] as String?,
            photo_url: urlAuthData['photo_url'] as String?,
            auth_date: urlAuthData['auth_date'] as int,
            hash: urlAuthData['hash'] as String,
          );

          // Выполняем авторизацию на сервере
          final authService = AuthService();
          final authResponse = await authService.telegramLogin(loginRequest);

          // Возвращаем результат
          if (mounted && Navigator.of(context).canPop()) {
            Navigator.of(context).pop(authResponse);
          }
        } else {
          print('❌ TelegramWebView: Данные авторизации неполные или отсутствуют');
          print('   Данные со страницы: $authData');
          print('   Данные из URL: $urlAuthData');
          throw Exception('Не удалось получить полные данные авторизации от Telegram');
        }
      }
    } catch (e) {
      print('❌ TelegramWebView: Ошибка обработки авторизации: $e');
      setState(() {
        _errorMessage = 'Ошибка авторизации: $e';
      });
    }
  }

  /// Извлекает данные авторизации со страницы
  Future<Map<String, dynamic>?> _extractAuthDataFromPage() async {
    try {
      // Сначала проверяем, есть ли данные, используя безопасный метод
      await _controller.runJavaScript('''
        window.flutterAuthCheck = (function() {
          try {
            console.log('🔍 Flutter: Checking for auth data...');
            console.log('Current URL:', window.location.href);
            console.log('Hash:', window.location.hash);

            // 1. Проверяем window.telegramAuthData (наша callback страница)
            if (typeof window.telegramAuthData !== 'undefined' && window.telegramAuthData) {
              console.log('✅ Found window.telegramAuthData');
              return JSON.stringify(window.telegramAuthData);
            }

            // 2. Проверяем fragment с tgAuthResult (base64)
            const hash = window.location.hash;
            if (hash && hash.includes('tgAuthResult=')) {
              console.log('✅ Found tgAuthResult in hash');
              try {
                const tgAuthResult = hash.split('tgAuthResult=')[1];
                const decodedData = atob(tgAuthResult);
                const userData = JSON.parse(decodedData);
                console.log('✅ Decoded auth data from hash:', userData);

                // Сохраняем для будущего использования
                window.telegramAuthData = userData;

                return JSON.stringify(userData);
              } catch (e) {
                console.log('❌ Error parsing tgAuthResult:', e);
              }
            }

            // 3. Проверяем localStorage
            if (typeof localStorage !== 'undefined') {
              const storedData = localStorage.getItem('telegram_auth_data');
              if (storedData) {
                console.log('✅ Found data in localStorage');
                return storedData;
              }
            }

            // 4. Проверяем URL параметры
            try {
              const urlParams = new URLSearchParams(window.location.search);
              if (urlParams.has('id')) {
                console.log('✅ Found data in URL params');
                const authData = {
                  id: parseInt(urlParams.get('id')),
                  first_name: urlParams.get('first_name'),
                  last_name: urlParams.get('last_name'),
                  username: urlParams.get('username'),
                  auth_date: parseInt(urlParams.get('auth_date')),
                  hash: urlParams.get('hash')
                };
                return JSON.stringify(authData);
              }
            } catch (e) {
              console.log('Error parsing URL params:', e);
            }

            // 5. Проверяем window.Telegram.WebApp
            if (typeof window.Telegram !== 'undefined' &&
                window.Telegram.WebApp &&
                window.Telegram.WebApp.initDataUnsafe) {
              const data = window.Telegram.WebApp.initDataUnsafe;
              if (data.user) {
                console.log('✅ Found data in Telegram WebApp');
                return JSON.stringify({
                  id: data.user.id,
                  first_name: data.user.first_name,
                  last_name: data.user.last_name,
                  username: data.user.username,
                  auth_date: data.auth_date,
                  hash: data.hash
                });
              }
            }

            console.log('❌ No auth data found');
            return null;
          } catch (e) {
            console.log('Error in auth check:', e);
            return null;
          }
        })();
      ''');

      // Теперь получаем результат
      final result = await _controller.runJavaScriptReturningResult('window.flutterAuthCheck');

      print('🔍 TelegramWebView: JavaScript результат: $result');

      if (result.toString() != 'null' && result.toString().isNotEmpty) {
        final jsonString = result.toString();
        // Убираем лишние кавычки, если они есть
        final cleanJsonString = jsonString.startsWith('"') && jsonString.endsWith('"')
            ? jsonString.substring(1, jsonString.length - 1)
            : jsonString;

        if (cleanJsonString.isNotEmpty && cleanJsonString != 'null') {
          print('✅ TelegramWebView: Парсим JSON: $cleanJsonString');
          return jsonDecode(cleanJsonString) as Map<String, dynamic>;
        }
      }

      // Если JavaScript не сработал, пробуем извлечь из URL напрямую
      print('⚠️ TelegramWebView: JavaScript не вернул данные, пробуем извлечь из URL');
      return await _extractAuthDataFromCurrentUrl();
    } catch (e) {
      print('❌ TelegramWebView: Ошибка извлечения данных: $e');
      return await _extractAuthDataFromCurrentUrl();
    }
  }

  /// Извлекает данные авторизации из текущего URL (fallback метод)
  Future<Map<String, dynamic>?> _extractAuthDataFromCurrentUrl() async {
    try {
      // Получаем текущий URL через JavaScript (безопасная версия)
      final currentUrl = await _controller.runJavaScriptReturningResult('''
        (function() {
          try {
            return window.location.href;
          } catch (e) {
            return 'about:blank';
          }
        })();
      ''');

      print('🔍 TelegramWebView: Текущий URL: $currentUrl');

      if (currentUrl.toString() != 'null' && currentUrl.toString() != 'about:blank') {
        final urlString = currentUrl.toString().replaceAll('"', '');

        try {
          final uri = Uri.parse(urlString);

          // Проверяем query параметры
          if (uri.queryParameters.containsKey('id')) {
            print('✅ TelegramWebView: Найдены данные в query параметрах URL');
            return {
              'id': int.tryParse(uri.queryParameters['id'] ?? '') ?? 0,
              'first_name': uri.queryParameters['first_name'],
              'last_name': uri.queryParameters['last_name'],
              'username': uri.queryParameters['username'],
              'auth_date': int.tryParse(uri.queryParameters['auth_date'] ?? '') ?? 0,
              'hash': uri.queryParameters['hash'],
            };
          }

          // Проверяем fragment (после #)
          if (uri.fragment.isNotEmpty) {
            print('🔍 TelegramWebView: Проверяем fragment: ${uri.fragment}');
            final fragmentParams = Uri.splitQueryString(uri.fragment);
            if (fragmentParams.containsKey('id')) {
              print('✅ TelegramWebView: Найдены данные в fragment URL');
              return {
                'id': int.tryParse(fragmentParams['id'] ?? '') ?? 0,
                'first_name': fragmentParams['first_name'],
                'last_name': fragmentParams['last_name'],
                'username': fragmentParams['username'],
                'auth_date': int.tryParse(fragmentParams['auth_date'] ?? '') ?? 0,
                'hash': fragmentParams['hash'],
              };
            }
          }
        } catch (e) {
          print('❌ TelegramWebView: Ошибка парсинга URL: $e');
        }
      }

      // Если ничего не найдено, ждем немного и пробуем еще раз
      print('⏳ TelegramWebView: Данные не найдены, ждем 2 секунды...');
      //await Future.delayed(const Duration(seconds: 2));

      // Повторная попытка получить данные (безопасная версия)
      final retryResult = await _controller.runJavaScriptReturningResult('''
        (function() {
          try {
            // Проверяем все возможные источники данных еще раз
            if (typeof window.Telegram !== 'undefined' &&
                window.Telegram.WebApp &&
                window.Telegram.WebApp.initDataUnsafe) {
              const data = window.Telegram.WebApp.initDataUnsafe;
              if (data.user) {
                return JSON.stringify({
                  id: data.user.id,
                  first_name: data.user.first_name,
                  last_name: data.user.last_name,
                  username: data.user.username,
                  auth_date: data.auth_date,
                  hash: data.hash
                });
              }
            }

            // Проверяем postMessage события
            if (typeof window.telegramAuthResult !== 'undefined') {
              return JSON.stringify(window.telegramAuthResult);
            }

            return null;
          } catch (e) {
            return null;
          }
        })();
      ''');

      if (retryResult.toString() != 'null') {
        print('✅ TelegramWebView: Получены данные при повторной попытке');
        final jsonString = retryResult.toString().replaceAll('"', '');
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }

      print('❌ TelegramWebView: Не удалось получить реальные данные авторизации');
      return null;

    } catch (e) {
      print('❌ TelegramWebView: Ошибка при извлечении данных из URL: $e');
      return null;
    }
  }

  /// Извлекает данные авторизации напрямую из URL (fallback метод)
  Future<Map<String, dynamic>?> _extractAuthDataFromUrl(String url) async {
    try {
      print('🔍 TelegramWebView: Извлечение данных из URL: $url');

      final uri = Uri.parse(url);

      // Проверяем query параметры
      if (uri.queryParameters.containsKey('id')) {
        print('✅ TelegramWebView: Найдены данные в query параметрах URL');
        return {
          'id': int.tryParse(uri.queryParameters['id'] ?? '') ?? 0,
          'first_name': uri.queryParameters['first_name'],
          'last_name': uri.queryParameters['last_name'],
          'username': uri.queryParameters['username'],
          'auth_date': int.tryParse(uri.queryParameters['auth_date'] ?? '') ?? 0,
          'hash': uri.queryParameters['hash'],
        };
      }

      // Проверяем fragment (после #)
      if (uri.fragment.isNotEmpty) {
        print('🔍 TelegramWebView: Проверяем fragment: ${uri.fragment}');
        final fragmentParams = Uri.splitQueryString(uri.fragment);
        if (fragmentParams.containsKey('id')) {
          print('✅ TelegramWebView: Найдены данные в fragment URL');
          return {
            'id': int.tryParse(fragmentParams['id'] ?? '') ?? 0,
            'first_name': fragmentParams['first_name'],
            'last_name': fragmentParams['last_name'],
            'username': fragmentParams['username'],
            'auth_date': int.tryParse(fragmentParams['auth_date'] ?? '') ?? 0,
            'hash': fragmentParams['hash'],
          };
        }
      }

      print('❌ TelegramWebView: Данные не найдены в URL');
      return null;

    } catch (e) {
      print('❌ TelegramWebView: Ошибка парсинга URL: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Вход через Telegram'),
        backgroundColor: const Color(0xFF0088CC), // Telegram blue
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Stack(
        children: [
          // WebView
          WebViewWidget(controller: _controller),
          
          // Индикатор загрузки
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Color(0xFF0088CC),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Загрузка Telegram...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          
          // Сообщение об ошибке
          if (_errorMessage != null)
            Container(
              color: Colors.white,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _errorMessage = null;
                          });
                          _initializeWebView();
                        },
                        child: const Text('Попробовать снова'),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
