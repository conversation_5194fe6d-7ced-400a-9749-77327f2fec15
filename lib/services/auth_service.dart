import '../models/api_response.dart';
import 'api_client.dart';
import 'telegram_service.dart';
import 'auth_state_service.dart';
import 'storage_service.dart';

/// Модель запроса для входа через Telegram
class TelegramLoginRequest {
  final int id;
  final String? first_name;
  final String? last_name;
  final String? username;
  final String? photo_url;
  final int auth_date;
  final String hash;

  TelegramLoginRequest({
    required this.id,
    this.first_name,
    this.last_name,
    this.username,
    this.photo_url,
    required this.auth_date,
    required this.hash,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    if (first_name != null) 'first_name': first_name,
    if (last_name != null) 'last_name': last_name,
    if (username != null) 'username': username,
    if (photo_url != null) 'photo_url': photo_url,
    'auth_date': auth_date,
    'hash': hash,
  };
}

/// Модель запроса для входа через Apple
class AppleLoginRequest {
  final String? identityToken;
  final String? authorizationCode;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? userIdentifier;

  AppleLoginRequest({
    required this.identityToken,
    this.authorizationCode,
    this.email,
    this.firstName,
    this.lastName,
    this.userIdentifier,
  });

  Map<String, dynamic> toJson() => {
    'identityToken': identityToken,
    if (authorizationCode != null) 'authorizationCode': authorizationCode,
    if (email != null) 'email': email,
    if (firstName != null) 'firstName': firstName,
    if (lastName != null) 'lastName': lastName,
    if (userIdentifier != null) 'userIdentifier': userIdentifier,
  };
}

/// Модель ответа при успешной авторизации
class AuthResponse {
  final String status;
  final String userId;
  final String accessToken;
  final String refreshToken;
  final DateTime? accessTokenExpiresAt;
  final DateTime? refreshTokenExpiresAt;

  AuthResponse({
    required this.status,
    required this.userId,
    required this.accessToken,
    required this.refreshToken,
    this.accessTokenExpiresAt,
    this.refreshTokenExpiresAt,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    try {
      print('📄 AuthResponse.fromJson: Parsing JSON: $json');

      final result = AuthResponse(
        status: json['status'] as String,
        userId: json['userId'] as String,
        accessToken: json['accessToken'] as String,
        refreshToken: json['refreshToken'] as String,
        accessTokenExpiresAt: json['accessTokenExpiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((json['accessTokenExpiresAt'] as int) * 1000)
            : null,
        refreshTokenExpiresAt: json['refreshTokenExpiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((json['refreshTokenExpiresAt'] as int) * 1000)
            : null,
      );

      print('✅ AuthResponse.fromJson: Successfully parsed');
      return result;
    } catch (e) {
      print('❌ AuthResponse.fromJson error: $e');
      print('📄 JSON data: $json');
      rethrow;
    }
  }
}

/// Модель ответа при обновлении токена
class RefreshTokenResponse {
  final String status;
  final String accessToken;
  final String? refreshToken;
  final DateTime? accessTokenExpiresAt;
  final DateTime? refreshTokenExpiresAt;

  RefreshTokenResponse({
    required this.status,
    required this.accessToken,
    this.refreshToken,
    this.accessTokenExpiresAt,
    this.refreshTokenExpiresAt,
  });

  factory RefreshTokenResponse.fromJson(Map<String, dynamic> json) {
    try {
      print('📄 RefreshTokenResponse.fromJson: Parsing JSON: $json');

      print('🔍 RefreshTokenResponse.fromJson: Проверяем поля:');
      print('   status: ${json['status']} (${json['status'].runtimeType})');

      final accessToken = json['accessToken']?.toString();
      print('   accessToken: ${accessToken != null && accessToken.length > 20 ? "${accessToken.substring(0, 20)}..." : accessToken ?? "null"} (${json['accessToken'].runtimeType})');

      final refreshToken = json['refreshToken']?.toString();
      print('   refreshToken: ${refreshToken != null && refreshToken.length > 20 ? "${refreshToken.substring(0, 20)}..." : refreshToken ?? "null"} (${json['refreshToken'].runtimeType})');

      print('   accessTokenExpiresAt: ${json['accessTokenExpiresAt']} (${json['accessTokenExpiresAt'].runtimeType})');
      print('   refreshTokenExpiresAt: ${json['refreshTokenExpiresAt']} (${json['refreshTokenExpiresAt'].runtimeType})');

      final result = RefreshTokenResponse(
        status: json['status'] as String,
        accessToken: json['accessToken'] as String,
        refreshToken: json['refreshToken'] != null ? json['refreshToken'] as String : null,
        accessTokenExpiresAt: json['accessTokenExpiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((json['accessTokenExpiresAt'] as int) * 1000)
            : null,
        refreshTokenExpiresAt: json['refreshTokenExpiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch((json['refreshTokenExpiresAt'] as int) * 1000)
            : null,
      );

      print('✅ RefreshTokenResponse.fromJson: Successfully parsed');
      return result;
    } catch (e) {
      print('❌ RefreshTokenResponse.fromJson error: $e');
      print('📄 JSON data: $json');
      rethrow;
    }
  }
}

/// Сервис для работы с аутентификацией
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;

  final ApiClient _apiClient;
  final AuthStateService _authStateService = AuthStateService();
  String? _accessToken;
  String? _refreshToken;
  String? _userId;
  DateTime? _accessTokenExpiresAt;
  DateTime? _refreshTokenExpiresAt;

  AuthService._internal() : _apiClient = ApiClient.instance {
    print('🔧 AuthService: Инициализация singleton с ApiClient: ${_apiClient.hashCode}');

    // Устанавливаем callback для автоматического обновления токена
    _apiClient.setRefreshTokenCallback(_handleTokenRefresh);
    // Устанавливаем callback для проверки валидности токена
    _apiClient.setTokenValidationCallback(_isTokenValid);
    // Устанавливаем callback для проверки наличия refresh токена
    _apiClient.setRefreshTokenCheckCallback(_hasRefreshToken);
    // Устанавливаем callback для выхода из системы
    _apiClient.setLogoutCallback(_handleLogout);

    print('✅ AuthService: Все callback\'ы установлены');

    // Загружаем сохраненные токены
    _loadTokensFromStorage();
  }

  /// Обработчик автоматического обновления токена
  Future<void> _handleTokenRefresh() async {
    await refreshAccessToken();
  }

  /// Обработчик проверки валидности токена
  bool _isTokenValid() {
    return isAccessTokenValid;
  }

  /// Обработчик проверки наличия refresh токена
  bool _hasRefreshToken() {
    final hasToken = _refreshToken != null;
    print('🔍 AuthService: Refresh токен ${hasToken ? "ЕСТЬ" : "ОТСУТСТВУЕТ"}');
    if (hasToken) {
      print('🔍 AuthService: Refresh токен: ${_refreshToken!.substring(0, 20)}...');
      if (_refreshTokenExpiresAt != null) {
        final isValid = isRefreshTokenValid;
        print('🔍 AuthService: Refresh токен валиден: $isValid');
        print('🔍 AuthService: Refresh токен истекает: $_refreshTokenExpiresAt');
      }
    }
    return hasToken;
  }

  /// Обработчик выхода из системы
  void _handleLogout() {
    print('🚪 AuthService: Обработка выхода из системы');
    logout();
    // _authStateService.logout() уже вызывается в методе logout()
  }

  /// Загрузка токенов из постоянного хранилища
  void _loadTokensFromStorage() {
    print('📂 AuthService._loadTokensFromStorage: Начинаем загрузку токенов из хранилища');

    if (!StorageService.hasAuthTokens()) {
      print('📂 AuthService._loadTokensFromStorage: Сохраненных токенов нет');
      return;
    }

    print('📂 AuthService._loadTokensFromStorage: Токены найдены, загружаем...');

    _accessToken = StorageService.getAccessToken();
    print('📂 AuthService._loadTokensFromStorage: Access токен: ${_accessToken != null ? "${_accessToken!.substring(0, 20)}..." : "НЕТ"}');

    _refreshToken = StorageService.getRefreshToken();
    print('📂 AuthService._loadTokensFromStorage: Refresh токен: ${_refreshToken != null ? "${_refreshToken!.substring(0, 20)}..." : "НЕТ"}');

    _userId = StorageService.getUserId();
    print('📂 AuthService._loadTokensFromStorage: User ID: ${_userId ?? "НЕТ"}');

    _accessTokenExpiresAt = StorageService.getAccessTokenExpiresAt();
    print('📂 AuthService._loadTokensFromStorage: Access токен истекает: ${_accessTokenExpiresAt ?? "НЕТ"}');

    _refreshTokenExpiresAt = StorageService.getRefreshTokenExpiresAt();
    print('📂 AuthService._loadTokensFromStorage: Refresh токен истекает: ${_refreshTokenExpiresAt ?? "НЕТ"}');

    // Устанавливаем токен в ApiClient
    if (_accessToken != null) {
      _apiClient.setAccessToken(_accessToken);
      print('🔑 AuthService._loadTokensFromStorage: Access токен установлен в ApiClient');

      // Уведомляем AuthStateService о том, что пользователь авторизован
      _authStateService.login();
      print('✅ AuthService._loadTokensFromStorage: AuthStateService уведомлен об авторизации');
    } else {
      print('❌ AuthService._loadTokensFromStorage: Access токен отсутствует, не устанавливаем в ApiClient');
    }

    print('✅ AuthService._loadTokensFromStorage: Загрузка токенов завершена');
  }

  /// Текущий access токен
  String? get accessToken => _accessToken;

  /// Текущий refresh токен
  String? get refreshToken => _refreshToken;

  /// ID текущего пользователя
  String? get userId => _userId;

  /// Время истечения access токена
  DateTime? get accessTokenExpiresAt => _accessTokenExpiresAt;

  /// Время истечения refresh токена
  DateTime? get refreshTokenExpiresAt => _refreshTokenExpiresAt;
  
  /// Проверяет, авторизован ли пользователь
  bool get isAuthenticated => _accessToken != null && _userId != null;

  /// Проверяет, валиден ли access токен (не истек ли)
  bool get isAccessTokenValid {
    if (_accessToken == null || _accessTokenExpiresAt == null) {
      print('🔍 AuthService: Access токен или время истечения отсутствует');
      return false;
    }

    // Добавляем буфер в 30 секунд для предотвращения гонки условий
    final bufferTime = DateTime.now().add(const Duration(seconds: 30));
    final isValid = _accessTokenExpiresAt!.isAfter(bufferTime);

    print('🔍 AuthService: Проверка валидности access токена:');
    print('   Текущее время + 30 сек: $bufferTime');
    print('   Токен истекает: $_accessTokenExpiresAt');
    print('   Токен валиден: $isValid');

    return isValid;
  }

  /// Проверяет, валиден ли refresh токен (не истек ли)
  bool get isRefreshTokenValid {
    if (_refreshToken == null || _refreshTokenExpiresAt == null) {
      print('🔍 AuthService: Refresh токен или время истечения отсутствует');
      return false;
    }

    final now = DateTime.now();
    final isValid = _refreshTokenExpiresAt!.isAfter(now);

    print('🔍 AuthService: Проверка валидности refresh токена:');
    print('   Текущее время: $now');
    print('   Токен истекает: $_refreshTokenExpiresAt');
    print('   Токен валиден: $isValid');

    return isValid;
  }

  /// Обновляет access токен используя refresh токен
  Future<void> refreshAccessToken() async {
    if (_refreshToken == null) {
      throw ApiException('Refresh токен отсутствует');
    }

    // Проверяем валидность refresh токена
    if (!isRefreshTokenValid) {
      print('❌ AuthService: Refresh токен истек');
      logout();
      throw ApiException('Refresh токен истек');
    }

    print('🔄 AuthService: Обновляем access токен');
    print('🔄 AuthService: Отправляем запрос к /api/auth/refresh');

    try {
      final response = await _apiClient.post(
        '/api/auth/refresh',
        body: {
          'refreshToken': _refreshToken,
        },
      );

      final refreshResponse = RefreshTokenResponse.fromJson(response);

      // Обновляем токены и время истечения
      _accessToken = refreshResponse.accessToken;
      // Обновляем refresh токен только если сервер прислал новый
      if (refreshResponse.refreshToken != null) {
        _refreshToken = refreshResponse.refreshToken;
        print('🔄 AuthService: Получен новый refresh токен');
      } else {
        print('🔄 AuthService: Сохраняем старый refresh токен');
      }
      _accessTokenExpiresAt = refreshResponse.accessTokenExpiresAt;
      // Обновляем время истечения refresh токена только если сервер прислал новое
      if (refreshResponse.refreshTokenExpiresAt != null) {
        _refreshTokenExpiresAt = refreshResponse.refreshTokenExpiresAt;
        print('🔄 AuthService: Получено новое время истечения refresh токена');
      } else {
        print('🔄 AuthService: Сохраняем старое время истечения refresh токена');
      }

      // Сохраняем обновленные токены в постоянное хранилище
      if (_refreshToken != null && _userId != null && _refreshTokenExpiresAt != null) {
        await StorageService.saveAuthTokens(
          accessToken: _accessToken!,
          refreshToken: _refreshToken!,
          userId: _userId!,
          accessTokenExpiresAt: _accessTokenExpiresAt!,
          refreshTokenExpiresAt: _refreshTokenExpiresAt!,
        );
      }

      // Устанавливаем новый токен в API клиент
      print('🔑 AuthService: Обновлен access токен: ${_accessToken!.substring(0, 20)}...');
      if (_accessTokenExpiresAt != null) {
        print('⏰ AuthService: Access токен истекает: $_accessTokenExpiresAt');
      }
      if (_refreshTokenExpiresAt != null) {
        print('⏰ AuthService: Refresh токен истекает: $_refreshTokenExpiresAt');
      }
      _apiClient.setAccessToken(_accessToken);

      print('✅ AuthService: Access токен успешно обновлен');

    } catch (e) {
      print('❌ AuthService refresh token error: $e');

      // Если обновление не удалось, очищаем токены
      logout();

      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка обновления токена: $e');
    }
  }

  /// Выполняет автоматический вход через Telegram WebApp
  ///
  /// Инициализирует Telegram WebApp и выполняет авторизацию
  ///
  /// Возвращает [AuthResponse] при успешной авторизации
  ///
  /// Возможные ошибки:
  /// - 'telegram_not_available' - Telegram WebApp недоступен
  /// - 'invalid_signature' - ошибка валидации подписи Telegram
  /// - 'expired_auth' - срок авторизации истёк
  Future<AuthResponse> loginWithTelegramWebApp() async {
    print('🔐 AuthService.loginWithTelegramWebApp called');

    final telegramService = TelegramService.instance;

    // Инициализируем Telegram WebApp
    if (!telegramService.isInitialized) {
      final initialized = await telegramService.initialize();
      if (!initialized) {
        throw ApiException('Telegram WebApp недоступен');
      }
    }

    // Создаем запрос авторизации из данных Telegram
    final loginRequest = telegramService.createLoginRequest();
    if (loginRequest == null) {
      throw ApiException('Не удалось получить данные авторизации из Telegram');
    }

    // Выполняем авторизацию
    return await telegramLogin(loginRequest);
  }

  /// Выполняет вход через Telegram
  ///
  /// Возвращает [AuthResponse] при успешной авторизации
  ///
  /// Параметры:
  /// - [request] - данные от Telegram WebApp
  ///
  /// Возможные ошибки:
  /// - 'invalid_signature' - ошибка валидации подписи Telegram
  /// - 'expired_auth' - срок авторизации истёк
  Future<AuthResponse> telegramLogin(TelegramLoginRequest request) async {
    print('🔐 AuthService.telegramLogin called for user: ${request.id}');

    try {
      final response = await _apiClient.post(
        '/api/auth/telegram',
        body: request.toJson(),
      );

      print('✅ AuthService: Telegram login successful');

      final authResponse = AuthResponse.fromJson(response);

      // Сохраняем токены, время истечения и ID пользователя
      _accessToken = authResponse.accessToken;
      _refreshToken = authResponse.refreshToken;
      _userId = authResponse.userId;
      _accessTokenExpiresAt = authResponse.accessTokenExpiresAt;
      _refreshTokenExpiresAt = authResponse.refreshTokenExpiresAt;

      // Сохраняем токены в постоянное хранилище
      await StorageService.saveAuthTokens(
        accessToken: _accessToken!,
        refreshToken: _refreshToken!,
        userId: _userId!,
        accessTokenExpiresAt: _accessTokenExpiresAt!,
        refreshTokenExpiresAt: _refreshTokenExpiresAt!,
      );

      // Устанавливаем токен в API клиент
      print('🔑 AuthService: Устанавливаем токен в ApiClient: ${_accessToken!.substring(0, 20)}...');
      print('🔍 AuthService: ApiClient instance: ${_apiClient.hashCode}');
      if (_accessTokenExpiresAt != null) {
        print('⏰ AuthService: Access токен истекает: $_accessTokenExpiresAt');
      }
      if (_refreshTokenExpiresAt != null) {
        print('⏰ AuthService: Refresh токен истекает: $_refreshTokenExpiresAt');
      }
      _apiClient.setAccessToken(_accessToken);

      // Уведомляем о успешной авторизации
      _authStateService.login();

      return authResponse;
    } catch (e) {
      print('❌ AuthService telegram login error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при входе через Telegram: $e');
    }
  }

  /// Выполняет вход через Apple ID
  ///
  /// Возвращает [AuthResponse] при успешной авторизации
  ///
  /// Параметры:
  /// - [request] - данные от Apple Sign In
  ///
  /// Возможные ошибки:
  /// - 'invalid_token' - невалидный identity token
  /// - 'token_expired' - токен истёк
  /// - 'user_not_found' - пользователь не найден
  Future<AuthResponse> appleLogin(AppleLoginRequest request) async {
    print('🍎 AuthService.appleLogin called');
    if (request.email != null) {
      print('   Email: ${request.email}');
    }

    try {
      final response = await _apiClient.post(
        '/api/auth/apple',
        body: request.toJson(),
      );

      print('✅ AuthService: Apple login successful');
      print('📄 AuthService: Parsing response: $response');

      final authResponse = AuthResponse.fromJson(response);

      // Сохраняем токены, время истечения и ID пользователя
      _accessToken = authResponse.accessToken;
      _refreshToken = authResponse.refreshToken;
      _userId = authResponse.userId;
      _accessTokenExpiresAt = authResponse.accessTokenExpiresAt;
      _refreshTokenExpiresAt = authResponse.refreshTokenExpiresAt;

      // Сохраняем токены в постоянное хранилище
      print('💾 AuthService.appleLogin: Сохраняем токены в StorageService');
      print('   Access Token: ${_accessToken!.substring(0, 20)}...');
      print('   Refresh Token: ${_refreshToken!.substring(0, 20)}...');
      print('   User ID: $_userId');
      print('   Access Token истекает: $_accessTokenExpiresAt');
      print('   Refresh Token истекает: $_refreshTokenExpiresAt');

      await StorageService.saveAuthTokens(
        accessToken: _accessToken!,
        refreshToken: _refreshToken!,
        userId: _userId!,
        accessTokenExpiresAt: _accessTokenExpiresAt!,
        refreshTokenExpiresAt: _refreshTokenExpiresAt!,
      );

      print('✅ AuthService.appleLogin: Токены сохранены в StorageService');

      // Устанавливаем токен в API клиент
      print('🔑 AuthService: Устанавливаем токен в ApiClient: ${_accessToken!.substring(0, 20)}...');
      if (_accessTokenExpiresAt != null) {
        print('⏰ AuthService: Access токен истекает: $_accessTokenExpiresAt');
      }
      if (_refreshTokenExpiresAt != null) {
        print('⏰ AuthService: Refresh токен истекает: $_refreshTokenExpiresAt');
      }
      _apiClient.setAccessToken(_accessToken);

      // Уведомляем о успешной авторизации
      _authStateService.login();

      return authResponse;
    } catch (e) {
      print('❌ AuthService apple login error: $e');
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Ошибка при входе через Apple: $e');
    }
  }



  /// Устанавливает токены вручную (например, при восстановлении сессии)
  void setTokens({
    required String accessToken,
    required String refreshToken,
    required String userId,
    DateTime? accessTokenExpiresAt,
    DateTime? refreshTokenExpiresAt,
  }) {
    _accessToken = accessToken;
    _refreshToken = refreshToken;
    _userId = userId;
    _accessTokenExpiresAt = accessTokenExpiresAt;
    _refreshTokenExpiresAt = refreshTokenExpiresAt;

    // Устанавливаем токен в API клиент
    _apiClient.setAccessToken(_accessToken);

    print('🔐 AuthService: Tokens set manually for user: $userId');
    if (accessTokenExpiresAt != null) {
      print('⏰ AuthService: Access токен истекает: $accessTokenExpiresAt');
    }
    if (refreshTokenExpiresAt != null) {
      print('⏰ AuthService: Refresh токен истекает: $refreshTokenExpiresAt');
    }
  }

  /// Выполняет выход из системы
  void logout() {
    _accessToken = null;
    _refreshToken = null;
    _userId = null;
    _accessTokenExpiresAt = null;
    _refreshTokenExpiresAt = null;

    // Очищаем токен в API клиенте
    _apiClient.setAccessToken(null);

    // Очищаем токены из постоянного хранилища
    StorageService.clearAuthTokens();

    // Уведомляем AuthStateService о выходе из системы
    _authStateService.logout();

    print('🚪 AuthService: User logged out');
  }

  /// Получает заголовки авторизации для API запросов
  Map<String, String>? getAuthHeaders() {
    if (_accessToken == null) return null;
    
    return {
      'Authorization': 'Bearer $_accessToken',
    };
  }

  /// Проверяет, нужно ли обновить токен и делает это автоматически
  Future<bool> ensureValidToken() async {
    if (_accessToken == null) return false;
    
    // В реальном приложении здесь можно проверить срок действия токена
    // и обновить его при необходимости
    
    return true;
  }

  /// Закрывает сервис и освобождает ресурсы
  void dispose() {
    _apiClient.dispose();
  }
}
