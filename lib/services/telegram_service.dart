import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'auth_service.dart';

/// Модель пользователя Telegram
class TelegramUser {
  final int id;
  final String? firstName;
  final String? lastName;
  final String? username;
  final String? languageCode;
  final bool? isPremium;

  TelegramUser({
    required this.id,
    this.firstName,
    this.lastName,
    this.username,
    this.languageCode,
    this.isPremium,
  });

  factory TelegramUser.fromJson(Map<String, dynamic> json) {
    return TelegramUser(
      id: json['id'] as int,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      languageCode: json['language_code'] as String?,
      isPremium: json['is_premium'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      if (firstName != null) 'first_name': firstName,
      if (lastName != null) 'last_name': lastName,
      if (username != null) 'username': username,
      if (languageCode != null) 'language_code': languageCode,
      if (isPremium != null) 'is_premium': isPremium,
    };
  }
}

/// Упрощенный сервис для работы с Telegram WebApp
class TelegramService {
  static TelegramService? _instance;
  bool _isInitialized = false;
  TelegramUser? _currentUser;

  TelegramService._();

  /// Singleton instance
  static TelegramService get instance {
    _instance ??= TelegramService._();
    return _instance!;
  }

  /// Проверяет, доступен ли Telegram WebApp
  bool get isAvailable {
    // Проверяем наличие Telegram WebApp через JavaScript
    if (kIsWeb) {
      // В веб-версии проверяем наличие window.Telegram.WebApp
      return _checkTelegramWebAppAvailable();
    }

    // В мобильных версиях проверяем через URL схему или другие методы
    return _checkMobileTelegramWebApp();
  }

  /// Проверяет доступность Telegram WebApp в веб-версии
  bool _checkTelegramWebAppAvailable() {
    try {
      // Этот код будет работать только в веб-версии
      // В реальном приложении здесь будет проверка window.Telegram?.WebApp
      return false; // Пока возвращаем false для симулятора
    } catch (e) {
      return false;
    }
  }

  /// Проверяет доступность Telegram WebApp в мобильной версии
  bool _checkMobileTelegramWebApp() {
    // Проверяем через platform channel
    _checkTelegramAvailabilityAsync();
    return false; // Синхронная проверка пока недоступна
  }

  /// Асинхронная проверка доступности Telegram WebApp
  Future<bool> _checkTelegramAvailabilityAsync() async {
    try {
      const platform = MethodChannel('telegram_webapp');
      final result = await platform.invokeMethod('isTelegramWebAppAvailable');
      return result as bool? ?? false;
    } catch (e) {
      print('❌ TelegramService: Ошибка проверки доступности: $e');
      return false;
    }
  }

  /// Проверяет, инициализирован ли WebApp
  bool get isInitialized => _isInitialized;

  /// Получает текущего пользователя
  TelegramUser? get currentUser => _currentUser;

  /// Инициализирует Telegram WebApp
  Future<bool> initialize() async {
    if (!isAvailable) {
      print('❌ TelegramService: Telegram WebApp недоступен, используем тестовые данные');
      return await _initializeWithTestData();
    }

    try {
      print('🔐 TelegramService: Инициализация реального Telegram WebApp');

      // Получаем данные из реального Telegram WebApp
      final webAppData = await _getTelegramWebAppData();

      if (webAppData != null) {
        _currentUser = webAppData['user'];
        _isInitialized = true;

        print('✅ TelegramService: Реальный WebApp инициализирован');
        print('   User ID: ${_currentUser!.id}');
        print('   Name: ${_currentUser!.firstName} ${_currentUser!.lastName}');

        return true;
      } else {
        print('❌ TelegramService: Не удалось получить данные WebApp');
        return await _initializeWithTestData();
      }
    } catch (e) {
      print('❌ TelegramService initialization error: $e');
      return await _initializeWithTestData();
    }
  }

  /// Инициализация с тестовыми данными
  Future<bool> _initializeWithTestData() async {
    _currentUser = TelegramUser(
      id: 52952911,
      firstName: 'Test',
      lastName: 'User',
      username: 'testuser',
      languageCode: 'ru',
      isPremium: false,
    );

    _isInitialized = true;
    print('✅ TelegramService: Инициализирован с тестовыми данными');
    print('   User ID: ${_currentUser!.id}');
    print('   Name: ${_currentUser!.firstName} ${_currentUser!.lastName}');

    return true;
  }

  /// Симулирует данные Telegram WebApp для тестирования
  Future<bool> simulateTelegramWebApp({
    required int userId,
    String? firstName,
    String? lastName,
    String? username,
  }) async {
    print('🧪 TelegramService: Симуляция Telegram WebApp');

    _currentUser = TelegramUser(
      id: userId,
      firstName: firstName,
      lastName: lastName,
      username: username,
      languageCode: 'ru',
      isPremium: false,
    );

    _isInitialized = true;

    print('✅ TelegramService: Симуляция завершена');
    print('   User ID: ${_currentUser!.id}');
    print('   Name: ${_currentUser!.firstName} ${_currentUser!.lastName}');

    return true;
  }

  /// Получает данные из реального Telegram WebApp
  Future<Map<String, dynamic>?> _getTelegramWebAppData() async {
    try {
      if (kIsWeb) {
        // В веб-версии получаем данные через JavaScript
        return await _getWebAppDataFromJS();
      } else {
        // В мобильной версии получаем данные через platform channels
        return await _getWebAppDataFromNative();
      }
    } catch (e) {
      print('❌ TelegramService: Ошибка получения данных WebApp: $e');
      return null;
    }
  }

  /// Получает данные WebApp через JavaScript (веб-версия)
  Future<Map<String, dynamic>?> _getWebAppDataFromJS() async {
    // В реальном приложении здесь будет вызов JavaScript:
    // window.Telegram.WebApp.initDataUnsafe
    // Пока возвращаем null
    return null;
  }

  /// Получает данные WebApp через native каналы (мобильная версия)
  Future<Map<String, dynamic>?> _getWebAppDataFromNative() async {
    try {
      const platform = MethodChannel('telegram_webapp');
      final result = await platform.invokeMethod('getTelegramData');

      if (result != null) {
        final data = Map<String, dynamic>.from(result);
        final userData = data['user'];

        if (userData != null) {
          return {
            'user': TelegramUser.fromJson(Map<String, dynamic>.from(userData)),
            'initData': data['initData'] as String?,
          };
        }
      }
    } catch (e) {
      print('❌ TelegramService: Platform channel error: $e');
    }

    return null;
  }

  /// Получает данные пользователя из Telegram
  TelegramUser? getTelegramUser() {
    if (!_isInitialized) {
      print('❌ TelegramService: WebApp не инициализирован');
      return null;
    }

    return _currentUser;
  }

  /// Получает initData для авторизации на сервере
  String? getInitData() {
    if (!_isInitialized || _currentUser == null) {
      print('❌ TelegramService: WebApp не инициализирован');
      return null;
    }

    // Создаем тестовые initData для разработки
    final authDate = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final userData = _currentUser!.toJson();
    final userDataString = jsonEncode(userData);

    return 'user=$userDataString&auth_date=$authDate&hash=test_hash_for_development';
  }

  /// Создает запрос для авторизации на основе данных Telegram
  TelegramLoginRequest? createLoginRequest() {
    final user = getTelegramUser();
    final initData = getInitData();

    if (user == null || initData == null) {
      print('❌ TelegramService: Недостаточно данных для авторизации');
      return null;
    }

    // Парсим initData для получения auth_date и hash
    final params = Uri.splitQueryString(initData);
    final authDateStr = params['auth_date'];
    final hash = params['hash'];

    if (authDateStr == null || hash == null) {
      print('❌ TelegramService: Отсутствуют auth_date или hash в initData');
      return null;
    }

    final authDate = int.tryParse(authDateStr);
    if (authDate == null) {
      print('❌ TelegramService: Неверный формат auth_date');
      return null;
    }

    return TelegramLoginRequest(
      id: user.id,
      first_name: user.firstName,
      last_name: user.lastName,
      username: user.username,
      photo_url: null, // В TelegramService нет photo_url, будет null
      auth_date: authDate,
      hash: hash,
    );
  }

  /// Показывает всплывающее уведомление в Telegram
  void showAlert(String message) {
    print('TelegramService Alert: $message');
    // В реальном приложении здесь будет вызов Telegram WebApp API
  }

  /// Показывает подтверждение в Telegram
  Future<bool> showConfirm(String message) async {
    print('TelegramService Confirm: $message');
    // В реальном приложении здесь будет вызов Telegram WebApp API
    return true; // В режиме разработки всегда подтверждаем
  }

  /// Показывает всплывающее окно в Telegram
  void showPopup({
    required String title,
    required String message,
  }) {
    print('TelegramService Popup: $title - $message');
    // В реальном приложении здесь будет вызов Telegram WebApp API
  }

  /// Включает/выключает вибрацию
  void hapticFeedback() {
    print('TelegramService: Haptic feedback');
    // В реальном приложении здесь будет вызов Telegram WebApp API
  }

  /// Получает информацию для отладки
  Map<String, dynamic> getDebugInfo() {
    if (!_isInitialized) {
      return {
        'initialized': false,
        'available': isAvailable,
        'error': 'WebApp не инициализирован',
      };
    }

    final user = getTelegramUser();

    return {
      'initialized': true,
      'available': isAvailable,
      'platform': 'test',
      'version': '1.0.0',
      'user': user != null ? {
        'id': user.id,
        'firstName': user.firstName,
        'lastName': user.lastName,
        'username': user.username,
        'languageCode': user.languageCode,
      } : null,
      'initData': getInitData()?.substring(0, 50) ?? 'null',
    };
  }
}
