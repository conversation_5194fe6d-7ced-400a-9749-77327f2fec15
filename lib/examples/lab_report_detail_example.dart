import '../medstata_api.dart';

/// Пример использования LabReportService.getLabReportDetail()
/// 
/// Демонстрирует получение детальной информации о лабораторном отчете
/// включая все анализы из этого отчета
void main() async {
  await labReportDetailExample();
}

Future<void> labReportDetailExample() async {
  print('📋 Пример получения детальной информации об отчете');
  print('=' * 55);

  final labReportService = LabReportService();

  try {
    // Сначала получаем список всех отчетов
    print('\n1️⃣ Получение списка отчетов...');
    final reportsResponse = await labReportService.getLabReports();

    if (reportsResponse.isSuccess && reportsResponse.data != null && reportsResponse.data!.isNotEmpty) {
      final reports = reportsResponse.data!;
      print('✅ Найдено отчетов: ${reports.length}');

      // Берем первый отчет для демонстрации
      final firstReport = reports.first;
      print('\n📄 Выбран отчет:');
      print('   ID: ${firstReport.id.substring(0, 20)}...');
      print('   Название: ${firstReport.analysisName ?? 'Без названия'}');
      print('   Дата: ${firstReport.formattedPerformedDate}');
      print('   Анализов: ${firstReport.testsCount}');

      // Получаем детальную информацию об отчете
      print('\n2️⃣ Получение детальной информации...');
      final detailResponse = await labReportService.getLabReportDetail(firstReport.id);

      if (detailResponse.isSuccess && detailResponse.data != null) {
        final detail = detailResponse.data!;
        final report = detail.report;
        final tests = detail.tests;

        print('✅ Детальная информация получена');

        // Информация об отчете
        print('\n📋 Информация об отчете:');
        print('   ID: ${report.id}');
        print('   Название: ${report.analysisName ?? 'Без названия'}');
        print('   Дата выполнения: ${report.formattedPerformedDate}');
        print('   Материал образца: ${report.specimenMaterial ?? 'Не указан'}');
        print('   Статус: ${report.status}');
        print('   Лаборатория: ${report.laboratoryName ?? 'Не указана'}');
        if (report.doctorName != null) {
          print('   Врач: ${report.doctorName}');
        }
        if (report.clinicName != null) {
          print('   Клиника: ${report.clinicName}');
        }

        // Статистика анализов
        print('\n📊 Статистика анализов:');
        final stats = detail.getTestsStatistics();
        print('   Всего анализов: ${stats['total']}');
        print('   Нормальных: ${stats['normal']}');
        print('   С отклонениями: ${stats['abnormal']}');
        print('   Других: ${stats['other']}');

        // Показываем первые 5 анализов
        print('\n🔬 Анализы (первые 5):');
        final displayTests = tests.take(5);
        for (int i = 0; i < displayTests.length; i++) {
          final test = displayTests.elementAt(i);
          final statusIcon = _getStatusIcon(test.status);
          final displayValue = _getDisplayValue(test);

          print('   ${i + 1}. $statusIcon ${test.name}');
          print('      Отображаемое значение: $displayValue');
          print('      Исходное значение: ${test.value.value} ${test.value.unitLabel}');
          if (test.value.referenceRange != null) {
            print('      Норма: ${test.value.referenceRange}');
          }
          if (test.interpretationReasons.isNotEmpty) {
            print('      Интерпретация: ${test.interpretationReasons.join(', ')}');
          }
        }

        if (tests.length > 5) {
          print('   ... и еще ${tests.length - 5} анализов');
        }

        // Анализы с отклонениями
        print('\n⚠️ Анализы с отклонениями:');
        final elevatedTests = detail.getTestsByStatus(LabTestStatus.elevated);
        final lowTests = detail.getTestsByStatus(LabTestStatus.low);
        final abnormalTests = [...elevatedTests, ...lowTests];

        if (abnormalTests.isNotEmpty) {
          for (final test in abnormalTests.take(3)) {
            print('   🔴 ${test.name}: ${test.value.value} ${test.value.unitLabel}');
            if (test.interpretationReasons.isNotEmpty) {
              print('      Причина: ${test.interpretationReasons.join(', ')}');
            }
          }
          if (abnormalTests.length > 3) {
            print('   ... и еще ${abnormalTests.length - 3} анализов с отклонениями');
          }
        } else {
          print('   ✅ Все анализы в норме');
        }

        // Демонстрация других методов
        print('\n🔍 Дополнительная информация:');
        print('   Количество анализов: ${detail.testsCount}');
        
        final normalTests = detail.getTestsByStatus(LabTestStatus.normal);
        print('   Нормальных анализов: ${normalTests.length}');

      } else if (detailResponse.isNoData) {
        print('❌ Отчет не найден');
      } else {
        print('❌ Ошибка получения деталей: ${detailResponse.message}');
      }

    } else if (reportsResponse.isNoData) {
      print('📭 У пользователя нет лабораторных отчетов');
      
      // Демонстрируем работу с тестовыми данными
      print('\n🧪 Демонстрация с тестовыми данными:');
      await demonstrateWithMockData();
      
    } else {
      print('❌ Ошибка получения отчетов: ${reportsResponse.message}');
    }

  } catch (e) {
    print('❌ Произошла ошибка: $e');
    
    if (e is ApiException) {
      print('   Тип: API Exception');
      print('   Код: ${e.statusCode}');
      print('   Сообщение: ${e.message}');
    }
  } finally {
    // Важно: всегда закрываем сервис
    labReportService.dispose();
    print('\n🔒 Сервис закрыт');
  }

  print('\n✨ Пример завершен');
}

/// Демонстрация работы с тестовыми данными
Future<void> demonstrateWithMockData() async {
  print('Создание тестового отчета с анализами...');

  final mockReport = LabReport(
    id: 'mock_report_123',
    analysisName: 'Биохимические исследования крови',
    performedAt: '2025-01-15',

    createdAt: '2025-01-10T10:00:00Z',
    specimenMaterial: 'сыворотка крови',
    status: LabReportStatus.completed,
    testsCount: 3,
    laboratoryName: 'Тестовая лаборатория',
    doctorName: 'Иванов И.И.',
  );

  final mockTests = [
    LabTest(
      id: 'test_1',
      name: 'Глюкоза',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '5.2',
        unitLabel: 'ммоль/л',
        referenceRange: '3.3-5.5',
      ),
    ),
    LabTest(
      id: 'test_2',
      name: 'Холестерин общий',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Повышен'],
      performedAt: '2025-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '6.8',
        unitLabel: 'ммоль/л',
        referenceRange: '3.0-5.2',
      ),
    ),
    LabTest(
      id: 'test_3',
      name: 'Белок общий',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '70',
        unitLabel: 'г/л',
        referenceRange: '64-83',
      ),
    ),
  ];

  final mockDetail = LabReportDetail(report: mockReport, tests: mockTests);

  print('📋 Тестовый отчет создан:');
  print('   Название: ${mockDetail.report.analysisName}');
  print('   Анализов: ${mockDetail.testsCount}');
  
  final stats = mockDetail.getTestsStatistics();
  print('   Нормальных: ${stats['normal']}');
  print('   С отклонениями: ${stats['abnormal']}');

  final elevatedTests = mockDetail.getTestsByStatus(LabTestStatus.elevated);
  final lowTests = mockDetail.getTestsByStatus(LabTestStatus.low);
  final abnormalTests = [...elevatedTests, ...lowTests];

  print('   Анализы с отклонениями:');
  for (final test in abnormalTests) {
    print('     - ${test.name}: ${test.value.value} ${test.value.unitLabel}');
  }
}

String _getStatusIcon(LabTestStatus status) {
  switch (status) {
    case LabTestStatus.normal:
      return '🟢';
    case LabTestStatus.elevated:
      return '🟠';
    case LabTestStatus.low:
      return '🟠';
    case LabTestStatus.other:
      return '🟡';
  }
}

String _getDisplayValue(LabTest test) {
  // Если есть численное значение и единицы измерения, показываем их
  if (test.value.value.isNotEmpty && test.value.unitLabel.isNotEmpty) {
    // Проверяем, является ли значение числом
    final numericValue = double.tryParse(test.value.value);
    if (numericValue != null) {
      return '${test.value.value} ${test.value.unitLabel}';
    }
  }

  // Если нет численного значения, показываем статус
  switch (test.status) {
    case LabTestStatus.normal:
      return 'Не обнаружено';
    case LabTestStatus.elevated:
      return 'Повышен';
    case LabTestStatus.low:
      return 'Понижен';
    case LabTestStatus.other:
      return 'Прочее';
  }
}

/// Запуск примера
void runExample() {
  labReportDetailExample();
}
