import '../medstata_api.dart';

/// Простой пример использования LabReportService
/// 
/// Демонстрирует основные методы для работы с лабораторными отчетами
void main() async {
  await simpleLabReportExample();
}

Future<void> simpleLabReportExample() async {
  print('📋 Простой пример LabReportService');
  print('=' * 40);

  final labReportService = LabReportService();

  try {
    // 1. Получение всех отчетов
    print('\n1️⃣ Получение всех отчетов...');
    final response = await labReportService.getLabReports();

    if (response.isSuccess && response.data != null) {
      final reports = response.data!;
      print('✅ Найдено отчетов: ${reports.length}');

      // Показываем первые 3 отчета
      final displayReports = reports.take(3);
      for (final report in displayReports) {
        print('   📄 ${report.analysisName ?? 'Анализ'}');
        print('      Статус: ${report.status}');
        print('      Анализов: ${report.testsCount}');
        print('      Дата: ${report.performedAt}');
      }

      if (reports.length > 3) {
        print('   ... и еще ${reports.length - 3} отчетов');
      }

      // 2. Статистика отчетов
      print('\n2️⃣ Статистика отчетов:');
      final stats = await labReportService.getLabReportsStatistics();
      if (stats != null) {
        print('   📊 Всего: ${stats['total']}');
        print('   ✅ Завершенных: ${stats['completed']}');
        print('   ⏳ В обработке: ${stats['processing']}');
        print('   ❌ С ошибками: ${stats['failed']}');
      }

      // 3. Общее количество анализов
      print('\n3️⃣ Общее количество анализов:');
      final totalTests = await labReportService.getTotalTestsCount();
      print('   🔬 Всего анализов: $totalTests');

      // 4. Завершенные отчеты
      print('\n4️⃣ Завершенные отчеты:');
      final completedReports = await labReportService.getCompletedLabReports();
      if (completedReports != null && completedReports.isNotEmpty) {
        print('   ✅ Завершенных отчетов: ${completedReports.length}');
        for (final report in completedReports.take(2)) {
          print('      - ${report.analysisName ?? "Анализ"} (${report.testsCount} анализов)');
        }
      } else {
        print('   📭 Нет завершенных отчетов');
      }

      // 5. Отчеты в обработке
      print('\n5️⃣ Отчеты в обработке:');
      final processingReports = await labReportService.getProcessingLabReports();
      if (processingReports != null && processingReports.isNotEmpty) {
        print('   ⏳ В обработке: ${processingReports.length}');
        for (final report in processingReports) {
          print('      - ${report.analysisName ?? "Анализ"} (${report.status})');
        }
      } else {
        print('   ✅ Нет отчетов в обработке');
      }

      // 6. Поиск отчета по ID
      if (reports.isNotEmpty) {
        final firstReport = reports.first;
        print('\n6️⃣ Поиск отчета по ID:');
        print('   🔍 Ищем отчет: ${firstReport.id}');
        
        final foundReport = await labReportService.getLabReportById(firstReport.id);
        if (foundReport != null) {
          print('   ✅ Найден: ${foundReport.analysisName ?? "Анализ"}');
        } else {
          print('   ❌ Не найден');
        }
      }

    } else if (response.status == 'no_data') {
      print('📭 У пользователя нет лабораторных отчетов');
      
      // Демонстрируем работу с пустыми данными
      print('\n📊 Статистика для пустого списка:');
      final stats = await labReportService.getLabReportsStatistics();
      print('   Статистика: ${stats ?? 'null'}');
      
      final totalTests = await labReportService.getTotalTestsCount();
      print('   Всего анализов: $totalTests');
      
      final hasReports = await labReportService.hasLabReports();
      print('   Есть отчеты: $hasReports');
      
    } else {
      print('❌ Ошибка получения отчетов: ${response.message}');
    }

  } catch (e) {
    print('❌ Произошла ошибка: $e');
    
    if (e is ApiException) {
      print('   Тип: API Exception');
      print('   Код: ${e.statusCode}');
      print('   Сообщение: ${e.message}');
    }
  } finally {
    // Важно: всегда закрываем сервис
    labReportService.dispose();
    print('\n🔒 Сервис закрыт');
  }

  print('\n✨ Пример завершен');
}

/// Демонстрация работы с моделью LabReport
void labReportModelDemo() {
  print('\n📋 Демонстрация модели LabReport');
  print('=' * 40);

  // Создание отчета из JSON (как приходит с сервера)
  final jsonData = {
    'id': 'report_abc123',
    'filename': 'blood_analysis_2025.pdf',
    'uploadedAt': '2025-01-15T14:30:00Z',
    'status': 'COMPLETED',
    'testsCount': 12,
    'fileSize': 1536000, // ~1.5 MB
    'description': 'Общий анализ крови'
  };

  final report = LabReport.fromJson(jsonData);

  print('📄 Отчет создан:');
  print('   ID: ${report.id}');
  print('   Файл: ${report.analysisName ?? "Анализ"}');
  print('   Статус: ${report.status}');
  print('   Завершен: ${report.isCompleted}');
  print('   В обработке: ${report.isProcessing}');
  print('   С ошибкой: ${report.isFailed}');
  print('   Анализов: ${report.testsCount}');
  print('   Размер: ${report.testsCount.toString() + " анализов"}');
  print('   Дата: ${report.performedAt}');
  print('   Время: ${report.performedAt}');
  print('   Описание: ${report.analysisName ?? "Описание недоступно"}');

  // Преобразование обратно в JSON
  final backToJson = report.toJson();
  print('\n🔄 JSON представление:');
  print('   $backToJson');

  print('\n✨ Демонстрация модели завершена');
}
