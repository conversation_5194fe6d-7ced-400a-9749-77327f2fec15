import '../medstata_api.dart';

/// Пример использования LabReportService
/// 
/// Этот файл демонстрирует основные возможности работы с лабораторными отчетами
/// через LabReportService API.
void main() async {
  await labReportServiceExample();
}

Future<void> labReportServiceExample() async {
  print('🚀 Пример использования LabReportService');
  print('=' * 50);

  final labReportService = LabReportService();

  try {
    // Получение всех отчетов
    print('\n📋 Получение всех лабораторных отчетов...');
    final response = await labReportService.getLabReports();

    if (response.isSuccess && response.data != null) {
      final reports = response.data!;
      print('✅ Получено отчетов: ${reports.length}');

      // Отображение информации о каждом отчете
      for (int i = 0; i < reports.length; i++) {
        final report = reports[i];
        print('\n📄 Отчет ${i + 1}:');
        print('   ID: ${report.id}');
        print('   Файл: ${report.analysisName ?? "Анализ"}');
        print('   Статус: ${report.status}');
        print('   Анализов: ${report.testsCount}');
        print('   Размер: ${report.testsCount.toString() + " анализов"}');
        print('   Загружен: ${report.performedAt} в ${report.performedAt}');
        if ((report.analysisName ?? "Описание недоступно") != "Описание недоступно") {
          print('   Описание: ${report.analysisName ?? "Описание недоступно"}');
        }
      }

      // Получение статистики отчетов
      print('\n📊 Статистика отчетов:');
      final stats = await labReportService.getLabReportsStatistics();
      if (stats != null) {
        print('   Всего отчетов: ${stats['total']}');
        print('   Завершенных: ${stats['completed']}');
        print('   В обработке: ${stats['processing']}');
        print('   С ошибками: ${stats['failed']}');
      }

      // Получение завершенных отчетов
      print('\n✅ Завершенные отчеты:');
      final completedReports = await labReportService.getCompletedLabReports();
      if (completedReports != null && completedReports.isNotEmpty) {
        for (final report in completedReports) {
          print('   - ${report.analysisName ?? "Анализ"} (${report.testsCount} анализов)');
        }
      } else {
        print('   Нет завершенных отчетов');
      }

      // Получение отчетов в обработке
      print('\n⏳ Отчеты в обработке:');
      final processingReports = await labReportService.getProcessingLabReports();
      if (processingReports != null && processingReports.isNotEmpty) {
        for (final report in processingReports) {
          print('   - ${report.analysisName ?? "Анализ"} (${report.status})');
        }
      } else {
        print('   Нет отчетов в обработке');
      }

      // Получение отчетов с ошибками
      print('\n❌ Отчеты с ошибками:');
      final failedReports = await labReportService.getFailedLabReports();
      if (failedReports != null && failedReports.isNotEmpty) {
        for (final report in failedReports) {
          print('   - ${report.analysisName ?? "Анализ"}');
        }
      } else {
        print('   Нет отчетов с ошибками');
      }

      // Общее количество анализов
      final totalTests = await labReportService.getTotalTestsCount();
      print('\n🔬 Общее количество анализов из всех отчетов: $totalTests');

      // Проверка наличия отчетов в обработке
      final hasProcessing = await labReportService.hasProcessingReports();
      if (hasProcessing) {
        print('\n⚠️  Есть отчеты в процессе обработки');
      }

      // Поиск отчета по ID (пример)
      if (reports.isNotEmpty) {
        final firstReportId = reports.first.id;
        print('\n🔍 Поиск отчета по ID: $firstReportId');
        final foundReport = await labReportService.getLabReportById(firstReportId);
        if (foundReport != null) {
          print('   ✅ Отчет найден: ${foundReport.analysisName ?? "Анализ"}');
        } else {
          print('   ❌ Отчет не найден');
        }
      }

    } else if (response.status == 'no_data') {
      print('📭 У пользователя нет лабораторных отчетов');
    } else {
      print('❌ Ошибка получения отчетов: ${response.message}');
    }

  } catch (e) {
    print('❌ Произошла ошибка: $e');
    
    if (e is ApiException) {
      print('   Код ошибки: ${e.statusCode}');
      print('   Сообщение: ${e.message}');
    }
  } finally {
    // Важно: всегда закрываем сервис
    labReportService.dispose();
    print('\n🔒 Сервис закрыт');
  }

  print('\n✨ Пример завершен');
}

/// Пример фильтрации отчетов по статусу
Future<void> filterReportsByStatusExample() async {
  print('\n🔍 Пример фильтрации отчетов по статусу');
  
  final labReportService = LabReportService();

  try {
    // Получение отчетов по каждому статусу
    final statuses = [
      LabReportStatus.pending,
      LabReportStatus.processing,
      LabReportStatus.completed,
      LabReportStatus.failed,
    ];

    for (final status in statuses) {
      final reports = await labReportService.getLabReportsByStatus(status);
      print('${status}: ${reports?.length ?? 0} отчетов');
    }

  } finally {
    labReportService.dispose();
  }
}

/// Пример работы с моделью LabReport
void labReportModelExample() {
  print('\n📋 Пример работы с моделью LabReport');

  // Создание отчета из JSON
  final json = {
    'id': 'report_123456',
    'filename': 'blood_test_results.pdf',
    'uploadedAt': '2025-01-15T14:30:00Z',
    'status': 'COMPLETED',
    'testsCount': 15,
    'fileSize': 2048576,
    'description': 'Результаты анализов крови от 15.01.2025'
  };

  final report = LabReport.fromJson(json);

  print('Отчет создан из JSON:');
  print('  ID: ${report.id}');
  print('  Файл: ${report.analysisName ?? "Анализ"}');
  print('  Статус: ${report.status}');
  print('  Завершен: ${report.isCompleted}');
  print('  В обработке: ${report.isProcessing}');
  print('  С ошибкой: ${report.isFailed}');
  print('  Дата: ${report.performedAt}');
  print('  Время: ${report.performedAt}');
  print('  Размер: ${report.testsCount.toString() + " анализов"}');

  // Преобразование обратно в JSON
  final backToJson = report.toJson();
  print('Преобразовано обратно в JSON: $backToJson');
}
