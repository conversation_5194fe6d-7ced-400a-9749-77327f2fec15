import 'package:flutter/material.dart';
import '../medstata_api.dart';

/// Демонстрационный экран для показа LabReportsScreen с тестовыми данными
class LabReportsDemoScreen extends StatelessWidget {
  const LabReportsDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Документы - Демо'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: const LabReportsScreen(),
    );
  }
}

/// Пример создания тестовых данных LabReport
class LabReportTestData {
  static List<LabReport> getSampleReports() {
    return [
      LabReport(
        id: 'report_1',
        analysisName: 'Хеликобактер антиген, качественно',
        performedAt: '2025-04-15T10:30:00Z',
        status: LabReportStatus.completed,
        testsCount: 1,
      ),
      LabReport(
        id: 'report_2',
        analysisName: 'Воздержание, эякулят и еще 26',
        performedAt: '2025-04-11T14:20:00Z',
        status: LabReportStatus.processing,
        testsCount: 26,
      ),
      LabReport(
        id: 'report_3',
        analysisName: 'Фагоцитоз, секрет простаты качественно и еще',
        performedAt: '2025-04-10T09:15:00Z',
        status: LabReportStatus.completed,
        testsCount: 1536000,
      ),
      LabReport(
        id: 'report_4',
        analysisName: 'Тестостерон общий',
        performedAt: '2025-04-01T16:45:00Z',
        status: LabReportStatus.completed,
        testsCount: 512000,
      ),
      LabReport(
        id: 'report_5',
        analysisName: 'Альбумин, сыворотка и еще 11',
        performedAt: '2025-03-27T11:30:00Z',
        status: LabReportStatus.completed,
        testsCount: 1800000,
      ),
      LabReport(
        id: 'report_6',
        analysisName: 'Холестерин и еще 2',
        performedAt: '2025-03-26T13:20:00Z',
        status: LabReportStatus.failed,
        testsCount: 768000,
      ),
    ];
  }

  /// Создает тестовый LabReportService с моковыми данными
  static LabReportService createMockService() {
    // Примечание: Это только для демонстрации
    // В реальном приложении данные будут приходить с сервера
    return LabReportService();
  }
}

/// Виджет для демонстрации отдельной карточки документа
class LabReportCardDemo extends StatelessWidget {
  final LabReport report;

  const LabReportCardDemo({
    super.key,
    required this.report,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Карточка документа'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Информация о документе:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('ID', report.id),
            _buildInfoRow('Файл', report.analysisName ?? "Анализ"),
            _buildInfoRow('Дата загрузки', report.performedAt),
            _buildInfoRow('Время загрузки', report.performedAt),
            _buildInfoRow('Статус', report.status.displayName),
            _buildInfoRow('Количество анализов', '${report.testsCount}'),
            _buildInfoRow('Размер файла', report.testsCount.toString() + " анализов"),
            if ((report.analysisName ?? "Описание недоступно") != "Описание недоступно")
              _buildInfoRow('Описание', report.analysisName ?? "Описание недоступно"),
            const SizedBox(height: 24),
            const Text(
              'Состояние:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            _buildStatusRow('Завершен', report.isCompleted),
            _buildStatusRow('В обработке', report.isProcessing),
            _buildStatusRow('С ошибкой', report.isFailed),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              color: value ? Colors.green : Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Пример использования в main.dart или для тестирования
void main() {
  runApp(MaterialApp(
    title: 'Lab Reports Demo',
    theme: ThemeData(
      primarySwatch: Colors.red,
      visualDensity: VisualDensity.adaptivePlatformDensity,
    ),
    home: const LabReportsDemoScreen(),
  ));
}

/// Функция для демонстрации работы с LabReport моделью
void demonstrateLabReportModel() {
  print('🧪 Демонстрация модели LabReport');
  print('=' * 40);

  final reports = LabReportTestData.getSampleReports();

  for (final report in reports) {
    print('\n📄 ${report.analysisName ?? "Анализ"}');
    print('   Статус: ${report.status}');
    print('   Анализов: ${report.testsCount}');
    print('   Размер: ${report.testsCount.toString() + " анализов"}');
    print('   Дата: ${report.performedAt}');
    print('   Завершен: ${report.isCompleted}');
    print('   В обработке: ${report.isProcessing}');
    print('   С ошибкой: ${report.isFailed}');
  }

  print('\n✨ Демонстрация завершена');
}
