import '../medstata_api.dart';

String _getStatusText(LabTestStatus status) {
  switch (status) {
    case LabTestStatus.normal:
      return 'Норма';
    case LabTestStatus.elevated:
      return 'Повышен';
    case LabTestStatus.low:
      return 'Понижен';
    case LabTestStatus.other:
      return 'Прочее';
  }
}

/// Демонстрация новой логики отображения значений анализов
/// 
/// Показывает, как работает _getDisplayValue:
/// - Численные значения показываются с единицами измерения
/// - Качественные анализы показывают статус
void main() {
  demonstrateDisplayValues();
}

void demonstrateDisplayValues() {
  print('📊 Демонстрация отображения значений анализов');
  print('=' * 50);

  // Примеры анализов с численными значениями
  final numericTests = [
    LabTest(
      id: 'cholesterol',
      name: 'Холестерин',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Повышен'],
      performedAt: '2025-03-28',
      value: LabTestValue(
        value: '6.13',
        unitLabel: 'ммоль/л',
        referenceRange: '3.0-5.2',
      ),
    ),
    LabTest(
      id: 'cholesterol_ldl',
      name: 'Холестерин ЛПВП',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-03-28',
      value: LabTestValue(
        value: '1.37',
        unitLabel: 'ммоль/л',
        referenceRange: '1.0-2.0',
      ),
    ),
    LabTest(
      id: 'cholesterol_ldl_low',
      name: 'Холестерин ЛПНП',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Повышен'],
      performedAt: '2025-03-28',
      value: LabTestValue(
        value: '3.56',
        unitLabel: 'ммоль/л',
        referenceRange: '2.0-3.3',
      ),
    ),
  ];

  // Примеры качественных анализов
  final qualitativeTests = [
    LabTest(
      id: 'helicobacter',
      name: 'Хеликобактер антиген, качественно',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-03-28',
      value: LabTestValue(
        value: 'отрицательный',
        unitLabel: '',
        referenceRange: 'отрицательный',
      ),
    ),
    LabTest(
      id: 'hepatitis_b',
      name: 'HBsAg (гепатит B)',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-03-28',
      value: LabTestValue(
        value: 'не обнаружен',
        unitLabel: '',
        referenceRange: 'не обнаружен',
      ),
    ),
    LabTest(
      id: 'covid_antibodies',
      name: 'COVID-19 антитела IgG',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Обнаружены'],
      performedAt: '2025-03-28',
      value: LabTestValue(
        value: 'положительный',
        unitLabel: '',
        referenceRange: 'отрицательный',
      ),
    ),
  ];

  print('\n🔢 Численные анализы (показываем значение + единицы):');
  print('─' * 55);
  for (final test in numericTests) {
    final displayValue = _getDisplayValue(test);
    final statusIcon = _getStatusIcon(test.status);
    
    print('$statusIcon ${test.name}');
    print('   Отображается: $displayValue');
    print('   Исходные данные: value="${test.value.value}", unit="${test.value.unitLabel}"');
    print('   Норма: ${test.value.referenceRange}');
    print('   Статус: ${_getStatusText(test.status)}');
    print('');
  }

  print('\n🧪 Качественные анализы (показываем статус):');
  print('─' * 55);
  for (final test in qualitativeTests) {
    final displayValue = _getDisplayValue(test);
    final statusIcon = _getStatusIcon(test.status);
    
    print('$statusIcon ${test.name}');
    print('   Отображается: $displayValue');
    print('   Исходные данные: value="${test.value.value}", unit="${test.value.unitLabel}"');
    print('   Норма: ${test.value.referenceRange}');
    print('   Статус: ${_getStatusText(test.status)}');
    print('');
  }

  print('\n📱 Как это выглядит в приложении:');
  print('─' * 55);
  print('┌─────────────────────────────────────────────────────┐');
  print('│ Биомаркеры                                          │');
  print('├─────────────────────────────────────────────────────┤');
  
  final allTests = [...numericTests, ...qualitativeTests];
  for (final test in allTests.take(4)) {
    final displayValue = _getDisplayValue(test);
    final statusIcon = _getStatusIcon(test.status);
    
    final name = test.name.length > 25 ? '${test.name.substring(0, 25)}...' : test.name;
    final value = displayValue.length > 15 ? '${displayValue.substring(0, 15)}...' : displayValue;
    
    print('│ $name${' ' * (30 - name.length)}$value $statusIcon │');
  }
  
  print('└─────────────────────────────────────────────────────┘');

  print('\n✨ Логика отображения:');
  print('   1. Если есть численное значение + единицы → показываем "6.13 ммоль/л"');
  print('   2. Если нет численного значения → показываем статус "Не обнаружено"');
  print('   3. Цветовая индикация по статусу анализа');

  // Демонстрация граничных случаев
  print('\n🔍 Граничные случаи:');
  print('─' * 30);
  
  final edgeCases = [
    LabTest(
      id: 'empty_value',
      name: 'Пустое значение',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-03-28',
      value: LabTestValue(value: '', unitLabel: ''),
    ),
    LabTest(
      id: 'no_unit',
      name: 'Без единиц',
      status: LabTestStatus.elevated,
      interpretationReasons: [],
      performedAt: '2025-03-28',
      value: LabTestValue(value: '123', unitLabel: ''),
    ),
    LabTest(
      id: 'text_value',
      name: 'Текстовое значение',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-03-28',
      value: LabTestValue(value: 'норма', unitLabel: 'условные единицы'),
    ),
  ];

  for (final test in edgeCases) {
    final displayValue = _getDisplayValue(test);
    print('${test.name}: "$displayValue"');
    print('   (value="${test.value.value}", unit="${test.value.unitLabel}")');
  }
}

String _getDisplayValue(LabTest test) {
  // Если есть численное значение и единицы измерения, показываем их
  if (test.value.value.isNotEmpty && test.value.unitLabel.isNotEmpty) {
    // Проверяем, является ли значение числом
    final numericValue = double.tryParse(test.value.value);
    if (numericValue != null) {
      return '${test.value.value} ${test.value.unitLabel}';
    }
  }
  
  // Если нет численного значения, показываем статус
  switch (test.status) {
    case LabTestStatus.normal:
      return 'Не обнаружено';
    case LabTestStatus.elevated:
      return 'Повышен';
    case LabTestStatus.low:
      return 'Понижен';
    case LabTestStatus.other:
      return 'Прочее';
  }
}

String _getStatusIcon(LabTestStatus status) {
  switch (status) {
    case LabTestStatus.normal:
      return '🟢';
    case LabTestStatus.elevated:
      return '🟠';
    case LabTestStatus.low:
      return '🟠';
    case LabTestStatus.other:
      return '🟡';
  }
}

/// Демонстрация с реальными данными API
void demonstrateWithRealApiData() {
  print('\n🌐 Пример с реальными данными API:');
  print('─' * 40);

  // Данные из реального API ответа
  final realTests = [
    LabTest(
      id: '1a281a388c02a88f92b149c6ceabf6a038b7285c6b0a4315efff1def53822f64',
      name: 'SHBG (глобулин)',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '28 мар. 2025',
      value: LabTestValue(
        value: '37.50',
        unitLabel: 'нмоль/л',
        referenceRange: '16.20–68.50',
      ),
    ),
    LabTest(
      id: 'f59efd5060c3d23c893d338459fd4d38ccc591e249419e1b1b3d9a749cd0c722',
      name: 'Аланинаминотрансфераза (АЛТ)',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Повышен'],
      performedAt: '28 мар. 2025',
      value: LabTestValue(
        value: '45.2',
        unitLabel: 'Ед/л',
        referenceRange: '7.0–40.0',
      ),
    ),
  ];

  for (final test in realTests) {
    final displayValue = _getDisplayValue(test);
    final statusIcon = _getStatusIcon(test.status);
    
    print('$statusIcon ${test.name}');
    print('   Отображается: $displayValue');
    print('   Норма: ${test.value.referenceRange}');
    if (test.interpretationReasons.isNotEmpty) {
      print('   Интерпретация: ${test.interpretationReasons.join(', ')}');
    }
    print('');
  }
}

/// Запуск всех демонстраций
void runAllDemos() {
  demonstrateDisplayValues();
  demonstrateWithRealApiData();
}
