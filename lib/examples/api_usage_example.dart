import '../medstata_api.dart';

/// Пример использования MedStata API
class ApiUsageExample {
  final PatientService _patientService;
  final LabTestService _labTestService;

  ApiUsageExample({
    PatientService? patientService,
    LabTestService? labTestService,
  }) : _patientService = patientService ?? PatientService(),
       _labTestService = labTestService ?? LabTestService();

  /// Пример аутентификации через Apple ID
  Future<void> authenticateExample() async {
    try {
      print('Аутентификация через Apple ID...');

      final appleService = AppleService.instance;
      final authService = AuthService();

      // Инициализируем Apple Service
      await appleService.initialize();

      // Проверяем доступность Apple Sign In
      if (!appleService.isAvailable) {
        print('❌ Apple Sign In недоступен на данной платформе');
        return;
      }

      // Выполняем Apple Sign In
      final appleData = await appleService.signIn();

      print('✅ Получены данные от Apple:');
      print('  Email: ${appleData?.email ?? 'не указан'}');
      print('  Name: ${appleData?.firstName ?? ''} ${appleData?.lastName ?? ''}');

      // Отправляем данные на сервер для авторизации
      if (appleData != null) {
        final authResponse = await authService.appleLogin(appleData);

        print('✅ Успешная авторизация:');
        print('  User ID: ${authResponse.userId}');
        print('  Access Token: ${authResponse.accessToken.substring(0, 20)}...');

        print('🔐 Токены автоматически сохранены и настроены в API клиентах');
      } else {
        print('❌ Не удалось получить данные от Apple');
      }

    } catch (e) {
      print('❌ Ошибка авторизации: $e');
    }
  }

  /// Пример получения данных пациента (требует авторизации)
  Future<void> getPatientExample() async {
    try {
      print('\nПолучение данных авторизованного пациента...');

      final response = await _patientService.getPatient();

      if (response.isSuccess) {
        final patient = response.data!;
        print('✅ Пациент найден:');
        print('  Возраст: ${patient.age} лет');
        print('  Пол: ${patient.gender.value}');
      } else if (response.isUserNotFound) {
        print('❌ Пользователь не найден');
      } else if (response.isNoData) {
        print('⚠️ Нет данных пациента для данного пользователя');
      } else {
        print('❌ Неизвестная ошибка: ${response.message}');
      }
    } catch (e) {
      print('❌ Ошибка при получении данных пациента: $e');
      if (e is ApiException && e.statusCode == 401) {
        print('  🔐 Требуется авторизация');
      }
    }
  }

  /// Пример получения всех лабораторных анализов
  Future<void> getLabTestsExample() async {
    try {
      print('\nПолучение лабораторных анализов для авторизованного пользователя...');

      final response = await _labTestService.getLabTests();

      if (response.isSuccess) {
        final data = response.data!;
        print('✅ Найдено анализов: ${data.totalCount}');
        print('  🟢 Нормальных: ${data.normalCount}');
        print('  🔴 Отклонений: ${data.abnormalCount}');
        print('  ⚪ Других: ${data.otherCount}');

        print('\n📊 Категории анализов:');
        for (final category in data.groupLabTests.keys) {
          final tests = data.groupLabTests[category]!;
          print('  📁 $category: ${tests.length} анализов');

          // Показываем первые несколько анализов в категории
          for (int i = 0; i < tests.length && i < 3; i++) {
            final test = tests[i];
            final statusIcon = test.status == LabTestStatus.normal ? '🟢' :
                              test.status == LabTestStatus.elevated || test.status == LabTestStatus.low ? '🔴' : '⚪';
            print('    $statusIcon ${test.name}: ${test.value.value} ${test.value.unitLabel} (${test.status.value})');
          }
          if (tests.length > 3) {
            print('    ... и еще ${tests.length - 3} анализов');
          }
        }
      } else if (response.isNoData) {
        print('⚠️ Нет данных анализов для данного пользователя');
      } else {
        print('❌ Ошибка: ${response.message}');
      }
    } catch (e) {
      print('❌ Ошибка при получении лабораторных анализов: $e');
      if (e is ApiException && e.statusCode == 401) {
        print('  🔐 Требуется авторизация');
      }
    }
  }

  /// Пример получения конкретного анализа с историей
  Future<void> getLabTestDetailExample() async {
    const testId = '30e9de0eec814774e7901dbe10e23d943a3a66d57a1c5f18210165994f9ae49e';

    try {
      print('\n🔍 Получение детальной информации об анализе...');
      print('   ID: ${testId.substring(0, 20)}...');

      final response = await _labTestService.getLabTest(testId);

      if (response.isSuccess) {
        final detail = response.data!;
        final current = detail.current;

        final statusIcon = current.status == LabTestStatus.normal ? '🟢' :
                          current.status == LabTestStatus.elevated || current.status == LabTestStatus.low ? '🔴' : '⚪';

        print('✅ Текущий анализ:');
        print('  📋 Название: ${current.name}');
        print('  📊 Значение: ${current.value.value} ${current.value.unitLabel}');
        print('  📏 Референсный диапазон: ${current.value.referenceRange ?? 'не указан'}');
        print('  $statusIcon Статус: ${current.status.value}');
        print('  📅 Дата: ${current.performedAt}');

        if (current.interpretationReasons.isNotEmpty) {
          print('  💡 Интерпретация:');
          for (final reason in current.interpretationReasons) {
            print('    - $reason');
          }
        }

        if (detail.history.isNotEmpty) {
          print('\n📈 История анализов (${detail.history.length} записей):');
          for (final historyItem in detail.history.take(5)) {
            final historyIcon = historyItem.status == LabTestStatus.normal ? '🟢' :
                               historyItem.status == LabTestStatus.elevated || historyItem.status == LabTestStatus.low ? '🔴' : '⚪';
            print('  $historyIcon ${historyItem.performedAt}: ${historyItem.value.value} ${historyItem.value.unitLabel} (${historyItem.status.value})');
          }
          if (detail.history.length > 5) {
            print('  ... и еще ${detail.history.length - 5} записей');
          }
        }
      } else if (response.isNoData) {
        print('❌ Анализ с данным ID не найден');
      } else {
        print('❌ Ошибка: ${response.message}');
      }
    } catch (e) {
      print('❌ Ошибка при получении детальной информации об анализе: $e');
      if (e is ApiException && e.statusCode == 401) {
        print('  🔐 Требуется авторизация');
      }
    }
  }

  /// Пример получения статистики анализов
  Future<void> getStatisticsExample() async {
    try {
      print('\n📊 Получение статистики анализов...');

      final stats = await _labTestService.getLabTestsStatistics();

      if (stats != null) {
        print('✅ Статистика анализов:');
        print('  📋 Всего: ${stats['total']}');
        print('  🟢 Нормальных: ${stats['normal']}');
        print('  🔴 С отклонениями: ${stats['abnormal']}');
        print('  ⚪ Других: ${stats['other']}');

        // Вычисляем проценты
        final total = stats['total'] as int;
        if (total > 0) {
          final normalPercent = ((stats['normal'] as int) * 100 / total).round();
          final abnormalPercent = ((stats['abnormal'] as int) * 100 / total).round();
          print('\n📈 Процентное соотношение:');
          print('  🟢 Нормальных: $normalPercent%');
          print('  🔴 С отклонениями: $abnormalPercent%');
        }
      } else {
        print('❌ Не удалось получить статистику');
      }
    } catch (e) {
      print('❌ Ошибка при получении статистики: $e');
      if (e is ApiException && e.statusCode == 401) {
        print('  🔐 Требуется авторизация');
      }
    }
  }

  /// Запуск всех примеров
  Future<void> runAllExamples() async {
    print('🚀 === Примеры использования MedStata API ===\n');

    // Сначала аутентификация
    await authenticateExample();

    // Проверяем, авторизован ли пользователь
    final authService = AuthService();
    if (authService.isAuthenticated) {
      print('\n✅ Пользователь авторизован, выполняем примеры API...');

      // Затем остальные примеры (требуют авторизации)
      await getPatientExample();
      await getLabTestsExample();
      await getLabTestDetailExample();
      await getStatisticsExample();
    } else {
      print('\n❌ Пользователь не авторизован, пропускаем примеры API');
    }

    print('\n🎉 === Примеры завершены ===');
  }

  /// Пример выхода из системы
  Future<void> logoutExample() async {
    try {
      print('\n🚪 Выход из системы...');
      final authService = AuthService();
      authService.logout();
      print('✅ Успешный выход из системы');
    } catch (e) {
      print('❌ Ошибка при выходе: $e');
    }
  }

  /// Освобождение ресурсов
  void dispose() {
    _patientService.dispose();
    _labTestService.dispose();
  }
}

/// Функция для запуска примеров
Future<void> main() async {
  final example = ApiUsageExample();

  try {
    await example.runAllExamples();

    // Демонстрируем выход из системы
    await example.logoutExample();

  } catch (e) {
    print('❌ Критическая ошибка: $e');
  } finally {
    example.dispose();
    print('\n🧹 Ресурсы освобождены');
  }
}
