import '../medstata_api.dart';

/// Демонстрация нового API метода /api/lab-reports/{report_id}
/// 
/// Показывает, как использовать LabReportService.getLabReportDetail()
/// для получения детальной информации об отчете с анализами
void main() async {
  await demonstrateLabReportDetailAPI();
}

Future<void> demonstrateLabReportDetailAPI() async {
  print('🚀 Демонстрация API /api/lab-reports/{report_id}');
  print('=' * 50);

  final labReportService = LabReportService();

  try {
    // Шаг 1: Получаем список всех отчетов
    print('\n📋 Шаг 1: Получение списка отчетов...');
    final reportsResponse = await labReportService.getLabReports();

    if (reportsResponse.isSuccess && reportsResponse.data != null) {
      final reports = reportsResponse.data!;
      print('✅ Получено отчетов: ${reports.length}');

      // Показываем краткую информацию о каждом отчете
      print('\n📄 Доступные отчеты:');
      for (int i = 0; i < reports.length && i < 3; i++) {
        final report = reports[i];
        print('   ${i + 1}. ${report.analysisName ?? 'Без названия'}');
        print('      ID: ${report.id.substring(0, 20)}...');
        print('      Дата: ${report.formattedPerformedDate}');
        print('      Анализов: ${report.testsCount}');
        print('      Статус: ${report.status}');
      }

      if (reports.length > 3) {
        print('   ... и еще ${reports.length - 3} отчетов');
      }

      // Шаг 2: Получаем детальную информацию о первом отчете
      if (reports.isNotEmpty) {
        final selectedReport = reports.first;
        print('\n🔍 Шаг 2: Получение деталей отчета...');
        print('   Выбран отчет: ${selectedReport.id.substring(0, 20)}...');

        final detailResponse = await labReportService.getLabReportDetail(selectedReport.id);

        if (detailResponse.isSuccess && detailResponse.data != null) {
          final detail = detailResponse.data!;
          
          print('✅ Детальная информация получена');
          
          // Показываем детальную информацию
          await _displayReportDetail(detail);
          
        } else if (detailResponse.isNoData) {
          print('❌ Отчет не найден');
        } else {
          print('❌ Ошибка получения деталей: ${detailResponse.message}');
        }
      }

    } else if (reportsResponse.isNoData) {
      print('📭 У пользователя нет отчетов');
      
      // Демонстрируем с тестовыми данными
      print('\n🧪 Демонстрация с тестовыми данными:');
      await _demonstrateWithTestData();
      
    } else {
      print('❌ Ошибка получения отчетов: ${reportsResponse.message}');
    }

  } catch (e) {
    print('❌ Произошла ошибка: $e');
    
    if (e is ApiException) {
      print('   Код ошибки: ${e.statusCode}');
      print('   Сообщение: ${e.message}');
    }
  } finally {
    labReportService.dispose();
    print('\n🔒 Сервис закрыт');
  }

  print('\n✨ Демонстрация завершена');
}

/// Отображает детальную информацию об отчете
Future<void> _displayReportDetail(LabReportDetail detail) async {
  final report = detail.report;
  final tests = detail.tests;

  print('\n📋 Детальная информация об отчете:');
  print('   ═══════════════════════════════════');
  print('   ID: ${report.id}');
  print('   Название: ${report.analysisName ?? 'Без названия'}');
  print('   Дата выполнения: ${report.formattedPerformedDate}');
  print('   Материал: ${report.specimenMaterial ?? 'Не указан'}');
  print('   Статус: ${report.status}');
  
  if (report.laboratoryName != null) {
    print('   Лаборатория: ${report.laboratoryName}');
  }
  if (report.doctorName != null) {
    print('   Врач: ${report.doctorName}');
  }
  if (report.clinicName != null) {
    print('   Клиника: ${report.clinicName}');
  }

  // Статистика анализов
  print('\n📊 Статистика анализов:');
  final stats = detail.getTestsStatistics();
  print('   Всего анализов: ${stats['total']}');
  print('   🟢 Нормальных: ${stats['normal']}');
  print('   🔴 С отклонениями: ${stats['abnormal']}');
  print('   🟡 Других: ${stats['other']}');

  // Показываем анализы
  print('\n🔬 Анализы из отчета:');
  for (int i = 0; i < tests.length && i < 5; i++) {
    final test = tests[i];
    final statusIcon = _getStatusIcon(test.status);
    
    print('   ${i + 1}. $statusIcon ${test.name}');
    print('      Значение: ${test.value.value} ${test.value.unitLabel}');
    
    if (test.value.referenceRange != null) {
      print('      Норма: ${test.value.referenceRange}');
    }
    
    if (test.interpretationReasons.isNotEmpty) {
      print('      Интерпретация: ${test.interpretationReasons.join(', ')}');
    }
    
    print('      Дата: ${test.performedAt}');
  }

  if (tests.length > 5) {
    print('   ... и еще ${tests.length - 5} анализов');
  }

  // Анализы с отклонениями
  final elevatedTests = detail.getTestsByStatus(LabTestStatus.elevated);
  final lowTests = detail.getTestsByStatus(LabTestStatus.low);
  final abnormalTests = [...elevatedTests, ...lowTests];
  if (abnormalTests.isNotEmpty) {
    print('\n⚠️ Анализы с отклонениями:');
    for (final test in abnormalTests.take(3)) {
      print('   🔴 ${test.name}: ${test.value.value} ${test.value.unitLabel}');
      if (test.interpretationReasons.isNotEmpty) {
        print('      Причина: ${test.interpretationReasons.join(', ')}');
      }
    }
  }
}

/// Демонстрация с тестовыми данными
Future<void> _demonstrateWithTestData() async {
  // Создаем тестовый отчет
  final testReport = LabReport(
    id: 'test_report_12345',
    analysisName: 'Биохимические исследования крови',
    performedAt: '2025-01-15',

    createdAt: '2025-01-10T10:00:00Z',
    specimenMaterial: 'сыворотка крови',
    status: LabReportStatus.completed,
    testsCount: 4,
    laboratoryName: 'Тестовая лаборатория',
    doctorName: 'Петров П.П.',
    clinicName: 'Медицинский центр',
  );

  // Создаем тестовые анализы
  final testTests = [
    LabTest(
      id: 'test_glucose',
      name: 'Глюкоза',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-01-15T10:30:00Z',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '5.2',
        unitLabel: 'ммоль/л',
        referenceRange: '3.3-5.5',
      ),
    ),
    LabTest(
      id: 'test_cholesterol',
      name: 'Холестерин общий',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Повышен'],
      performedAt: '2025-01-15T10:30:00Z',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '6.8',
        unitLabel: 'ммоль/л',
        referenceRange: '3.0-5.2',
      ),
    ),
    LabTest(
      id: 'test_protein',
      name: 'Белок общий',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-01-15T10:30:00Z',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '70',
        unitLabel: 'г/л',
        referenceRange: '64-83',
      ),
    ),
    LabTest(
      id: 'test_hemoglobin',
      name: 'Гемоглобин',
      status: LabTestStatus.low,
      interpretationReasons: ['Понижен'],
      performedAt: '2025-01-15T10:30:00Z',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(
        value: '110',
        unitLabel: 'г/л',
        referenceRange: '120-160',
      ),
    ),
  ];

  final testDetail = LabReportDetail(report: testReport, tests: testTests);

  print('📋 Создан тестовый отчет с анализами');
  await _displayReportDetail(testDetail);
}

String _getStatusIcon(LabTestStatus status) {
  switch (status) {
    case LabTestStatus.normal:
      return '🟢';
    case LabTestStatus.elevated:
      return '🔴';
    case LabTestStatus.low:
      return '🔵';

    case LabTestStatus.other:
      return '🟡';
  }
}

/// Демонстрация различных методов LabReportDetail
void demonstrateLabReportDetailMethods() {
  print('\n🔧 Демонстрация методов LabReportDetail:');
  print('=' * 40);

  // Создаем пример отчета
  final report = LabReport(
    id: 'demo_report',
    analysisName: 'Демо отчет',
    performedAt: '2025-01-15',

    createdAt: '2025-01-10T10:00:00Z',
    status: LabReportStatus.completed,
    testsCount: 3,
  );

  final tests = [
    LabTest(
      id: '1',
      name: 'Тест 1',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(value: '5.0', unitLabel: 'ед'),
    ),
    LabTest(
      id: '2',
      name: 'Тест 2',
      status: LabTestStatus.elevated,
      interpretationReasons: ['Повышен'],
      performedAt: '2025-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(value: '8.0', unitLabel: 'ед'),
    ),
    LabTest(
      id: '3',
      name: 'Тест 3',
      status: LabTestStatus.normal,
      interpretationReasons: [],
      performedAt: '2025-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      value: LabTestValue(value: '3.0', unitLabel: 'ед'),
    ),
  ];

  final detail = LabReportDetail(report: report, tests: tests);

  print('Количество анализов: ${detail.testsCount}');
  print('Статистика: ${detail.getTestsStatistics()}');
  print('Нормальные анализы: ${detail.getTestsByStatus(LabTestStatus.normal).length}');
  final elevatedCount = detail.getTestsByStatus(LabTestStatus.elevated).length;
  final lowCount = detail.getTestsByStatus(LabTestStatus.low).length;
  print('Анализы с отклонениями: ${elevatedCount + lowCount}');
  print('toString(): ${detail.toString()}');
}
