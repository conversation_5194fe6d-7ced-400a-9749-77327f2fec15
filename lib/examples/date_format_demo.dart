import '../medstata_api.dart';

/// Демонстрация нового формата дат в LabReport
void main() {
  demonstrateDateFormatting();
}

void demonstrateDateFormatting() {
  print('📅 Демонстрация форматирования дат в LabReport');
  print('=' * 50);

  // Создаем тестовые отчеты с разными датами
  final testReports = [
    LabReport(
      id: 'report_1',
      analysisName: 'Биохимические исследования крови',
      performedAt: '2025-03-28',

      createdAt: '2025-01-10T10:00:00Z',
      createdAt: '2025-03-25T10:00:00Z',
      status: LabReportStatus.completed,
      testsCount: 12,
      laboratoryName: 'Инвитро AstraLab',
    ),
    LabReport(
      id: 'report_2',
      analysisName: 'Общий анализ крови',
      performedAt: '2025-01-30',

      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.completed,
      testsCount: 17,
      laboratoryName: 'КДЛ',
      doctorName: 'Альтшулер Б. Ю.',
    ),
    LabReport(
      id: 'report_3',
      analysisName: 'Анализ на витамины',
      performedAt: '2025-12-15',

      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.processing,
      testsCount: 8,
    ),
    LabReport(
      id: 'report_4',
      analysisName: 'Гормональные исследования',
      performedAt: '2024-06-05',

      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.completed,
      testsCount: 5,
    ),
  ];

  print('\n📋 Примеры форматирования дат:');
  print('-' * 40);

  for (int i = 0; i < testReports.length; i++) {
    final report = testReports[i];
    print('\n${i + 1}. ${report.analysisName}');
    print('   Исходная дата: ${report.performedAt}');
    print('   Отформатированная: ${report.formattedPerformedDate}');
    print('   Полная дата: ${report.formattedFullDate}');
    print('   Статус: ${report.status}');
    if (report.laboratoryName != null) {
      print('   Лаборатория: ${report.laboratoryName}');
    }
    if (report.doctorName != null) {
      print('   Врач: ${report.doctorName}');
    }
  }

  print('\n📱 Как это будет выглядеть в интерфейсе:');
  print('-' * 40);

  for (final report in testReports) {
    final statusIcon = _getStatusIcon(report.status);
    print('\n┌─────────────────────────────────────┐');
    print('│ 📄  ${report.formattedPerformedDate.padRight(25)} $statusIcon│');
    print('│     ${_truncateText(report.analysisName ?? 'Анализ', 30).padRight(30)} │');
    if (report.isCompleted) {
      print('│                                   🗑️📤│');
    } else {
      print('│                                     │');
    }
    print('└─────────────────────────────────────┘');
  }

  print('\n✨ Демонстрация завершена');
}

String _getStatusIcon(LabReportStatus status) {
  switch (status) {
    case LabReportStatus.completed:
      return '✅';
    case LabReportStatus.processing:
    case LabReportStatus.pending:
      return '⏳';
    case LabReportStatus.failed:
      return '❌';
  }
}

String _truncateText(String text, int maxLength) {
  if (text.length <= maxLength) {
    return text;
  }
  return '${text.substring(0, maxLength - 3)}...';
}

/// Тестирование различных форматов входных дат
void testDifferentDateFormats() {
  print('\n🧪 Тестирование различных форматов дат:');
  print('=' * 50);

  final testDates = [
    '2025-03-28',           // Стандартный формат API
    '2025-01-01',           // Новый год
    '2025-12-31',           // Конец года
    '2024-02-29',           // Високосный год
    '2025-06-15T10:30:00Z', // Полная дата с временем
  ];

  for (final dateString in testDates) {
    try {
      final report = LabReport(
        id: 'test',
        performedAt: dateString,

        createdAt: '2025-01-10T10:00:00Z',
        status: LabReportStatus.completed,
        testsCount: 1,
      );

      print('\nИсходная дата: $dateString');
      print('Результат: ${report.formattedPerformedDate}');
    } catch (e) {
      print('\nИсходная дата: $dateString');
      print('Ошибка: $e');
    }
  }
}

/// Демонстрация сортировки по датам
void demonstrateDateSorting() {
  print('\n📊 Демонстрация сортировки по датам:');
  print('=' * 50);

  final reports = [
    LabReport(
      id: '1',
      analysisName: 'Старый анализ',
      performedAt: '2024-01-15',

      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.completed,
      testsCount: 5,
    ),
    LabReport(
      id: '2',
      analysisName: 'Недавний анализ',
      performedAt: '2025-03-20',

      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.completed,
      testsCount: 8,
    ),
    LabReport(
      id: '3',
      analysisName: 'Самый новый анализ',
      performedAt: '2025-03-28',

      createdAt: '2025-01-10T10:00:00Z',
      status: LabReportStatus.processing,
      testsCount: 12,
    ),
  ];

  print('\nДо сортировки:');
  for (final report in reports) {
    print('  ${report.formattedPerformedDate} - ${report.analysisName}');
  }

  // Сортируем по дате (сначала новые)
  reports.sort((a, b) => b.performedAt.compareTo(a.performedAt));

  print('\nПосле сортировки (сначала новые):');
  for (final report in reports) {
    print('  ${report.formattedPerformedDate} - ${report.analysisName}');
  }
}

/// Запуск всех демонстраций
void runAllDemos() {
  demonstrateDateFormatting();
  testDifferentDateFormats();
  demonstrateDateSorting();
}
