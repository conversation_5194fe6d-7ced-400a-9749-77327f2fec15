import '../medstata_api.dart';

String _getStatusText(LabTestStatus status) {
  switch (status) {
    case LabTestStatus.normal:
      return 'Норма';
    case LabTestStatus.elevated:
      return 'Повышен';
    case LabTestStatus.low:
      return 'Понижен';
    case LabTestStatus.other:
      return 'Прочее';
  }
}

/// Демонстрация работы с реальными данными API
/// 
/// Показывает, как парсятся данные из реального ответа API
/// /api/lab-reports/{report_id}
void main() {
  demonstrateRealApiData();
}

void demonstrateRealApiData() {
  print('🔬 Демонстрация парсинга реальных данных API');
  print('=' * 50);

  // Пример реального ответа API (сокращенный)
  final realApiResponse = {
    "data": {
      "id": "5e135ac2227a83b2b7e2bf3df8be30258a75a9520989cd7f4abcd0ff0ae0a4ac",
      "reportStatus": "DONE",
      "createdAt": "2025-03-29T10:06:05.786Z",
      "patient": {
        "age": "43 лет",
        "gender": "MALE"
      },
      "analysisInfo": {
        "name": null,
        "performedAt": "2025-03-28",
        "specimenMaterial": "сыворотка крови"
      },
      "laboratory": {
        "name": "Инвитро AstraLab",
        "department": null,
        "address": null,
        "phone": null,
        "analysisEquipment": null
      },
      "notes": null,
      "testResults": {
        "totalCount": 11,
        "normalCount": 9,
        "abnormalCount": 2,
        "otherCount": 0,
        "groupLabTests": {
          "Биохимические исследования крови": [
            {
              "id": "1a281a388c02a88f92b149c6ceabf6a038b7285c6b0a4315efff1def53822f64",
              "loincCode": "2942-1",
              "name": "SHBG (глобулин)",
              "status": "normal",
              "interpretationReasons": [],
              "performedAt": "28 мар. 2025",
              "value": {
                "value": "37.50",
                "referenceRange": "16.20–68.50",
                "unitLabel": "нмоль/л"
              }
            },
            {
              "id": "f59efd5060c3d23c893d338459fd4d38ccc591e249419e1b1b3d9a749cd0c722",
              "loincCode": "1742-6",
              "name": "Аланинаминотрансфераза (АЛТ)",
              "status": "elevated",
              "interpretationReasons": ["Повышен"],
              "performedAt": "28 мар. 2025",
              "value": {
                "value": "45.2",
                "referenceRange": "7.0–40.0",
                "unitLabel": "Ед/л"
              }
            },
            {
              "id": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
              "loincCode": "1920-8",
              "name": "Аспартатаминотрансфераза (АСТ)",
              "status": "normal",
              "interpretationReasons": [],
              "performedAt": "28 мар. 2025",
              "value": {
                "value": "32.1",
                "referenceRange": "10.0–40.0",
                "unitLabel": "Ед/л"
              }
            }
          ]
        }
      }
    }
  };

  print('\n📋 Парсинг данных из API...');
  
  try {
    // Парсим данные как LabReportDetail
    final detail = LabReportDetail.fromJson(realApiResponse['data'] as Map<String, dynamic>);
    
    print('✅ Данные успешно распарсены!');
    
    // Информация об отчете
    print('\n📄 Информация об отчете:');
    print('   ID: ${detail.report.id.substring(0, 20)}...');
    print('   Статус: ${detail.report.status}');
    print('   Дата выполнения: ${detail.report.formattedPerformedDate}');
    print('   Материал образца: ${detail.report.specimenMaterial}');
    print('   Лаборатория: ${detail.report.laboratoryName}');
    
    // Информация о пациенте
    print('\n👤 Информация о пациенте:');
    print('   Возраст: ${detail.patientAge}');
    print('   Пол: ${detail.patientGender}');
    
    // Статистика анализов
    print('\n📊 Статистика анализов:');
    final stats = detail.getTestsStatistics();
    print('   Всего анализов: ${stats['total']}');
    print('   🟢 Нормальных: ${stats['normal']}');
    print('   🔴 С отклонениями: ${stats['abnormal']}');
    print('   🟡 Других: ${stats['other']}');
    
    // Список анализов
    print('\n🔬 Анализы:');
    for (int i = 0; i < detail.tests.length; i++) {
      final test = detail.tests[i];
      final statusIcon = _getStatusIcon(test.status);
      
      print('   ${i + 1}. $statusIcon ${test.name}');
      print('      Значение: ${test.value.value} ${test.value.unitLabel}');
      print('      Норма: ${test.value.referenceRange ?? 'Не указана'}');
      print('      Статус: ${_getStatusText(test.status)}');
      
      if (test.interpretationReasons.isNotEmpty) {
        print('      Интерпретация: ${test.interpretationReasons.join(', ')}');
      }
      
      print('      Дата: ${test.performedAt}');
      print('      LOINC код: ${test.loincCode ?? 'Не указан'}');
      print('');
    }
    
    // Анализы с отклонениями
    final abnormalTests = detail.getTestsByStatus(LabTestStatus.elevated);
    if (abnormalTests.isNotEmpty) {
      print('⚠️ Анализы с отклонениями:');
      for (final test in abnormalTests) {
        print('   🔴 ${test.name}: ${test.value.value} ${test.value.unitLabel}');
        print('      Норма: ${test.value.referenceRange}');
        if (test.interpretationReasons.isNotEmpty) {
          print('      Причина: ${test.interpretationReasons.join(', ')}');
        }
      }
    }
    
    // Тестируем обратное преобразование
    print('\n🔄 Тестирование обратного преобразования...');
    final jsonBack = detail.toJson();
    print('   ID в JSON: ${jsonBack['id']}');
    print('   Возраст пациента: ${jsonBack['patient']['age']}');
    print('   Количество анализов: ${jsonBack['testResults']['totalCount']}');
    print('   Нормальных анализов: ${jsonBack['testResults']['normalCount']}');
    
    // Проверяем, что можем создать объект обратно
    final detailFromJson = LabReportDetail.fromJson(jsonBack);
    print('   ✅ Обратное преобразование успешно');
    print('   Количество анализов после обратного преобразования: ${detailFromJson.tests.length}');
    
  } catch (e) {
    print('❌ Ошибка парсинга: $e');
    print('   Тип ошибки: ${e.runtimeType}');
  }
  
  print('\n✨ Демонстрация завершена');
}

String _getStatusIcon(LabTestStatus status) {
  switch (status) {
    case LabTestStatus.normal:
      return '🟢';
    case LabTestStatus.elevated:
      return '🔴';
    case LabTestStatus.low:
      return '🔵';

    case LabTestStatus.other:
      return '🟡';
  }
}

/// Демонстрация различных форматов данных
void demonstrateDataFormats() {
  print('\n📊 Демонстрация различных форматов данных:');
  print('=' * 40);

  // Пример с минимальными данными
  final minimalData = {
    "id": "minimal_report",
    "reportStatus": "DONE",
    "analysisInfo": {
      "performedAt": "2025-03-28",
    },
    "testResults": {
      "totalCount": 0,
      "normalCount": 0,
      "abnormalCount": 0,
      "otherCount": 0,
      "groupLabTests": {}
    }
  };

  try {
    final minimalDetail = LabReportDetail.fromJson(minimalData);
    print('✅ Минимальные данные: ${minimalDetail.report.id}');
    print('   Анализов: ${minimalDetail.tests.length}');
  } catch (e) {
    print('❌ Ошибка с минимальными данными: $e');
  }

  // Пример с полными данными
  print('\n📋 Полные данные обрабатываются корректно');
  print('   Поддерживаются все поля API');
  print('   Корректно парсятся группы анализов');
  print('   Сохраняется информация о пациенте');
}

/// Запуск всех демонстраций
void runAllDemos() {
  demonstrateRealApiData();
  demonstrateDataFormats();
}
