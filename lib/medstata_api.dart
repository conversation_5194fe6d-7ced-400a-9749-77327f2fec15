/// MedStata API клиент для Flutter
/// 
/// Этот пакет предоставляет классы и сервисы для работы с MedStata API,
/// включая получение данных пациентов и лабораторных анализов.
library medstata_api;

// Модели данных
export 'models/api_response.dart';
export 'models/patient.dart';
export 'models/lab_test.dart';
export 'models/lab_report.dart';

// Сервисы
export 'services/api_client.dart';
export 'services/auth_service.dart';
export 'services/telegram_service.dart';
export 'services/apple_service.dart';
export 'services/patient_service.dart';
export 'services/lab_test_service.dart';
export 'services/lab_report_service.dart';

// Экраны
export 'screens/auth_screen.dart';
export 'screens/main_navigation_screen.dart';
export 'screens/lab_tests_main_screen.dart';
export 'screens/lab_reports_screen.dart';
export 'screens/lab_report_detail_screen.dart';
export 'screens/upload_screen.dart';
export 'screens/settings_screen.dart';
