

import 'lab_test.dart';
import 'api_response.dart';

/// Информация о документе лабораторного отчета
class LabReportDocument {
  final String? originalFileName;
  final String? fileType;
  final int? fileSizeBytes;

  LabReportDocument({
    this.originalFileName,
    this.fileType,
    this.fileSizeBytes,
  });

  factory LabReportDocument.fromJson(Map<String, dynamic> json) {
    return LabReportDocument(
      originalFileName: json['originalFileName']?.toString(),
      fileType: json['fileType']?.toString(),
      fileSizeBytes: json['fileSizeBytes'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'originalFileName': originalFileName,
      'fileType': fileType,
      'fileSizeBytes': fileSizeBytes,
    };
  }

  /// Возвращает размер файла в человекочитаемом формате
  String get formattedFileSize {
    if (fileSizeBytes == null) return 'Неизвестно';

    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '$bytes Б';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} КБ';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} МБ';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} ГБ';
  }

  /// Возвращает иконку для типа файла
  String get fileTypeIcon {
    if (fileType == null) return '📄';

    switch (fileType!.toUpperCase()) {
      case 'PDF':
        return '📄';
      case 'JPG':
      case 'JPEG':
      case 'PNG':
      case 'GIF':
        return '🖼️';
      case 'DOC':
      case 'DOCX':
        return '📝';
      case 'XLS':
      case 'XLSX':
        return '📊';
      default:
        return '📄';
    }
  }

  @override
  String toString() {
    return 'LabReportDocument(fileName: $originalFileName, type: $fileType, size: $fileSizeBytes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LabReportDocument &&
        other.originalFileName == originalFileName &&
        other.fileType == fileType &&
        other.fileSizeBytes == fileSizeBytes;
  }

  @override
  int get hashCode => Object.hash(originalFileName, fileType, fileSizeBytes);
}

/// Статус обработки лабораторного отчета
enum LabReportStatus {
  pending('PENDING'),
  processing('PROCESSING'),
  completed('COMPLETED'),
  failed('FAILED');

  const LabReportStatus(this.value);
  final String value;

  static LabReportStatus fromString(String? value) {
    switch (value?.toUpperCase()) {
      case 'PENDING':
        return LabReportStatus.pending;
      case 'PROCESSING':
        return LabReportStatus.processing;
      case 'COMPLETED':
        return LabReportStatus.completed;
      case 'FAILED':
        return LabReportStatus.failed;
      default:
        return LabReportStatus.pending;
    }
  }

  String get displayName {
    switch (this) {
      case LabReportStatus.pending:
        return 'Ожидает обработки';
      case LabReportStatus.processing:
        return 'Обрабатывается';
      case LabReportStatus.completed:
        return 'Обработан';
      case LabReportStatus.failed:
        return 'Ошибка обработки';
    }
  }
}

/// Лабораторный отчет (загруженный файл с результатами анализов)
class LabReport {
  final String id;
  final String? analysisName;
  final String performedAt;
  final String createdAt;
  final String? specimenMaterial;
  final LabReportStatus status;
  final int testsCount;
  final String? clinicName;
  final String? laboratoryName;
  final String? doctorName;
  final LabReportDocument? document;

  LabReport({
    required this.id,
    this.analysisName,
    required this.performedAt,
    required this.createdAt,
    this.specimenMaterial,
    required this.status,
    required this.testsCount,
    this.clinicName,
    this.laboratoryName,
    this.doctorName,
    this.document,
  });

  factory LabReport.fromJson(Map<String, dynamic> json) {
    // Извлекаем данные лаборатории
    final laboratory = json['laboratory'] as Map<String, dynamic>?;
    final doctor = json['doctor'] as Map<String, dynamic>?;
    final clinic = json['clinic'] as Map<String, dynamic>?;

    // Извлекаем данные документа
    final documentJson = json['document'] as Map<String, dynamic>?;
    final document = documentJson != null ? LabReportDocument.fromJson(documentJson) : null;

    return LabReport(
      id: json['id']?.toString() ?? '',
      analysisName: json['analysisName']?.toString(),
      performedAt: json['performedAt']?.toString() ?? '',
      createdAt: json['createdAt']?.toString() ?? '',
      specimenMaterial: json['specimenMaterial']?.toString(),
      status: _mapReportStatus(json['reportStatus']?.toString()),
      testsCount: json['testCount'] as int? ?? 0,
      clinicName: clinic?['name']?.toString(),
      laboratoryName: laboratory?['name']?.toString(),
      doctorName: doctor?['name']?.toString(),
      document: document,
    );
  }

  static LabReportStatus _mapReportStatus(String? status) {
    switch (status?.toUpperCase()) {
      case 'DONE':
        return LabReportStatus.completed;
      case 'PROCESSING':
        return LabReportStatus.processing;
      case 'PENDING':
        return LabReportStatus.pending;
      case 'ERROR':
      case 'FAILED':
        return LabReportStatus.failed;
      default:
        return LabReportStatus.pending;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'analysisName': analysisName,
      'performedAt': performedAt,
      'createdAt': createdAt,
      'specimenMaterial': specimenMaterial,
      'reportStatus': status.value,
      'testCount': testsCount,
      'clinic': clinicName != null ? {'name': clinicName} : null,
      'laboratory': laboratoryName != null ? {'name': laboratoryName} : null,
      'doctor': doctorName != null ? {'name': doctorName} : null,
      'document': document?.toJson(),
    };
  }

  /// Возвращает true, если отчет успешно обработан
  bool get isCompleted => status == LabReportStatus.completed;

  /// Возвращает true, если отчет находится в процессе обработки
  bool get isProcessing => status == LabReportStatus.processing || status == LabReportStatus.pending;

  /// Возвращает true, если при обработке отчета произошла ошибка
  bool get isFailed => status == LabReportStatus.failed;

  /// Возвращает отформатированную дату выполнения анализа
  String get formattedPerformedDate {
    try {
      // Если дата в формате "2025-03-28"
      if (performedAt.contains('-') && performedAt.length == 10) {
        final parts = performedAt.split('-');
        if (parts.length == 3) {
          final year = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final day = int.parse(parts[2]);
          final months = [
            'янв', 'фев', 'мар', 'апр', 'май', 'июн',
            'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'
          ];
          return '$day ${months[month - 1]} $year';
        }
      }

      // Пытаемся парсить как полную дату
      final dateTime = DateTime.parse(performedAt);
      final months = [
        'янв', 'фев', 'мар', 'апр', 'май', 'июн',
        'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'
      ];
      return '${dateTime.day} ${months[dateTime.month - 1]} ${dateTime.year}';
    } catch (e) {
      return performedAt;
    }
  }

  /// Возвращает полную отформатированную дату
  String get formattedFullDate {
    try {
      if (performedAt.contains('-') && performedAt.length == 10) {
        final parts = performedAt.split('-');
        if (parts.length == 3) {
          return '${parts[2]}.${parts[1]}.${parts[0]}';
        }
      }
      final dateTime = DateTime.parse(performedAt);
      return '${dateTime.day.toString().padLeft(2, '0')}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year}';
    } catch (e) {
      return performedAt;
    }
  }

  /// Возвращает отформатированную дату создания отчета
  String get formattedCreatedDate {
    try {
      final dateTime = DateTime.parse(createdAt);
      final months = [
        'янв', 'фев', 'мар', 'апр', 'май', 'июн',
        'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'
      ];
      return '${dateTime.day} ${months[dateTime.month - 1]} ${dateTime.year}';
    } catch (e) {
      return createdAt;
    }
  }

  @override
  String toString() {
    return 'LabReport(id: $id, analysisName: $analysisName, status: ${status.value}, testsCount: $testsCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LabReport && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Детальная информация о лабораторном отчете с анализами
class LabReportDetail {
  final LabReport report;
  final List<LabTest> tests;
  final String? patientAge;
  final String? patientGender;
  final String? notes;

  LabReportDetail({
    required this.report,
    required this.tests,
    this.patientAge,
    this.patientGender,
    this.notes,
  });

  factory LabReportDetail.fromJson(Map<String, dynamic> json) {
    // Создаем LabReport из основных данных
    final report = LabReport(
      id: json['id']?.toString() ?? '',
      analysisName: json['analysisInfo']?['name']?.toString(),
      performedAt: json['analysisInfo']?['performedAt']?.toString() ?? '',
      createdAt: json['createdAt']?.toString() ?? '',
      specimenMaterial: json['analysisInfo']?['specimenMaterial']?.toString(),
      status: LabReport._mapReportStatus(json['reportStatus']?.toString()),
      testsCount: json['testResults']?['totalCount'] as int? ?? 0,
      laboratoryName: json['laboratory']?['name']?.toString(),
      doctorName: null, // Не предоставляется в этом API
      clinicName: json['laboratory']?['department']?.toString(),
      document:  json["document"] != null ? LabReportDocument.fromJson(json["document"] as Map<String, dynamic>) : null,
    );

    // Извлекаем анализы из groupLabTests
    final List<LabTest> tests = [];
    final testResults = json['testResults'] as Map<String, dynamic>?;
    if (testResults != null) {
      final groupLabTests = testResults['groupLabTests'] as Map<String, dynamic>?;
      if (groupLabTests != null) {
        // Проходим по всем группам анализов
        groupLabTests.forEach((groupName, groupTests) {
          if (groupTests is List) {
            for (final testJson in groupTests) {
              if (testJson is Map<String, dynamic>) {
                tests.add(LabTest.fromJson(testJson));
              }
            }
          }
        });
      }
    }

    return LabReportDetail(
      report: report,
      tests: tests,
      patientAge: json['patient']?['age']?.toString(),
      patientGender: json['patient']?['gender']?.toString(),
      notes: json['notes']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    // Группируем анализы по категориям (пока используем одну группу)
    final Map<String, List<Map<String, dynamic>>> groupLabTests = {
      'Биохимические исследования крови': tests.map((test) => test.toJson()).toList(),
    };

    return {
      'id': report.id,
      'reportStatus': report.status.value,
      'patient': {
        'age': patientAge,
        'gender': patientGender,
      },
      'analysisInfo': {
        'name': report.analysisName,
        'performedAt': report.performedAt,
        'specimenMaterial': report.specimenMaterial,
      },
      'laboratory': {
        'name': report.laboratoryName,
        'department': report.clinicName,
      },
      'notes': notes,
      'testResults': {
        'totalCount': tests.length,
        'normalCount': tests.where((t) => t.status == LabTestStatus.normal).length,
        'abnormalCount': tests.where((t) => t.status == LabTestStatus.elevated || t.status == LabTestStatus.low).length,
        'otherCount': tests.where((t) => t.status == LabTestStatus.other).length,
        'groupLabTests': groupLabTests,
      },
    };
  }

  /// Возвращает количество анализов в отчете
  int get testsCount => tests.length;

  /// Возвращает анализы с определенным статусом
  List<LabTest> getTestsByStatus(LabTestStatus status) {
    return tests.where((test) => test.status == status).toList();
  }

  /// Возвращает анализы определенной категории
  List<LabTest> getTestsByCategory(String category) {
    // Здесь можно добавить логику группировки по категориям
    // Пока возвращаем все анализы
    return tests;
  }

  /// Возвращает статистику анализов в отчете
  Map<String, int> getTestsStatistics() {
    final normalTests = tests.where((test) => test.status == LabTestStatus.normal).length;
    final abnormalTests = tests.where((test) => test.status == LabTestStatus.elevated || test.status == LabTestStatus.low).length;
    final otherTests = tests.where((test) => test.status == LabTestStatus.other).length;

    return {
      'total': tests.length,
      'normal': normalTests,
      'abnormal': abnormalTests,
      'other': otherTests,
    };
  }

  @override
  String toString() {
    return 'LabReportDetail(reportId: ${report.id}, testsCount: ${tests.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LabReportDetail && other.report.id == report.id;
  }

  @override
  int get hashCode => report.id.hashCode;
}
