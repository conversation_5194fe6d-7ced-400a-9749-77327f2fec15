import 'api_response.dart';

/// Модель данных пациента
class Patient {
  final int age;
  final Gender gender;

  Patient({
    required this.age,
    required this.gender,
  });

  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      age: json['age'] as int,
      gender: Gender.fromString(json['gender'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'age': age,
      'gender': gender.value,
    };
  }

  @override
  String toString() {
    return 'Patient(age: $age, gender: ${gender.value})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Patient && 
           other.age == age && 
           other.gender == gender;
  }

  @override
  int get hashCode => age.hashCode ^ gender.hashCode;
}
