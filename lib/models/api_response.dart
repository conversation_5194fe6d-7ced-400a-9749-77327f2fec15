/// Базовые модели для API ответов
class ApiResponse<T> {
  final String status;
  final T? data;
  final String? message;

  ApiResponse({
    required this.status,
    this.data,
    this.message,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      status: json['status'] as String,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'] as T?,
      message: json['message'] as String?,
    );
  }

  /// Создает успешный ответ с данными
  factory ApiResponse.success(T data) {
    return ApiResponse<T>(
      status: 'ok',
      data: data,
    );
  }

  /// Создает ответ об ошибке
  factory ApiResponse.error(String message) {
    return ApiResponse<T>(
      status: 'error',
      message: message,
    );
  }

  /// Создает ответ об отсутствии данных
  factory ApiResponse.noData(String message) {
    return ApiResponse<T>(
      status: 'no_data',
      message: message,
    );
  }

  bool get isSuccess => status == 'ok';
  bool get isUserNotFound => status == 'user_not_found';
  bool get isNoData => status == 'no_data';
}

/// Статусы лабораторных анализов
enum LabTestStatus {
  normal('NORMAL'),
  elevated('ELEVATED'),
  low('LOW'),
  other('OTHER');

  const LabTestStatus(this.value);
  final String value;

  static LabTestStatus fromString(String? value) {
    if (value == null || value.isEmpty) {
      return LabTestStatus.other;
    }

    // Приводим к верхнему регистру для сравнения
    final upperValue = value.toUpperCase();

    return LabTestStatus.values.firstWhere(
      (status) => status.value == upperValue,
      orElse: () => LabTestStatus.other,
    );
  }
}

/// Пол пациента
enum Gender {
  male('MALE'),
  female('FEMALE'),
  other('OTHER');

  const Gender(this.value);
  final String value;

  static Gender fromString(String value) {
    return Gender.values.firstWhere(
      (gender) => gender.value == value,
      orElse: () => Gender.other,
    );
  }
}
