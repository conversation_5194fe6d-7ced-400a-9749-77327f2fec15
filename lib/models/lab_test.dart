import 'api_response.dart';

/// Значение лабораторного анализа
class LabTestValue {
  final String value;
  final String? referenceRange;
  final String unitLabel;

  LabTestValue({
    required this.value,
    this.referenceRange,
    required this.unitLabel,
  });

  factory LabTestValue.fromJson(Map<String, dynamic> json) {
    return LabTestValue(
      value: json['value']?.toString() ?? '',
      referenceRange: json['referenceRange']?.toString(),
      unitLabel: json['unitLabel']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'referenceRange': referenceRange,
      'unitLabel': unitLabel,
    };
  }
}

/// Лабораторный анализ
class LabTest {
  final String id;
  final String? loincCode;
  final String name;
  final LabTestStatus status;
  final List<String> interpretationReasons;
  final String performedAt;
  final LabTestValue value;

  LabTest({
    required this.id,
    this.loincCode,
    required this.name,
    required this.status,
    required this.interpretationReasons,
    required this.performedAt,
    required this.value,
  });

  factory LabTest.fromJson(Map<String, dynamic> json) {
    return LabTest(
      id: json['id']?.toString() ?? '',
      loincCode: json['loincCode']?.toString(),
      name: json['name']?.toString() ?? '',
      status: LabTestStatus.fromString(json['status']?.toString()),
      interpretationReasons: List<String>.from(json['interpretationReasons'] ?? []),
      performedAt: json['performedAt']?.toString() ?? '',
      value: LabTestValue.fromJson(json['value'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'loincCode': loincCode,
      'name': name,
      'status': status.value,
      'interpretationReasons': interpretationReasons,
      'performedAt': performedAt,
      'value': value.toJson(),
    };
  }
}

/// Краткая информация о лабораторном анализе для истории
class LabTestHistoryItem {
  final String id;
  final String name;
  final LabTestStatus status;
  final String performedAt;
  final LabTestValue value;

  LabTestHistoryItem({
    required this.id,
    required this.name,
    required this.status,
    required this.performedAt,
    required this.value,
  });

  factory LabTestHistoryItem.fromJson(Map<String, dynamic> json) {
    return LabTestHistoryItem(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      status: LabTestStatus.fromString(json['status']?.toString()),
      performedAt: json['performedAt']?.toString() ?? '',
      value: LabTestValue.fromJson(json['value'] as Map<String, dynamic>),
    );
  }
}

/// Группированные лабораторные анализы
class GroupedLabTests {
  final int totalCount;
  final int normalCount;
  final int abnormalCount;
  final int otherCount;
  final Map<String, List<LabTest>> groupLabTests;

  GroupedLabTests({
    required this.totalCount,
    required this.normalCount,
    required this.abnormalCount,
    required this.otherCount,
    required this.groupLabTests,
  });

  factory GroupedLabTests.fromJson(Map<String, dynamic> json) {
    final groupLabTestsJson = json['groupLabTests'] as Map<String, dynamic>;
    final Map<String, List<LabTest>> groupLabTests = {};
    
    groupLabTestsJson.forEach((key, value) {
      final List<dynamic> testsList = value as List<dynamic>;
      groupLabTests[key] = testsList
          .map((testJson) => LabTest.fromJson(testJson as Map<String, dynamic>))
          .toList();
    });

    return GroupedLabTests(
      totalCount: json['totalCount'] as int,
      normalCount: json['normalCount'] as int,
      abnormalCount: json['abnormalCount'] as int,
      otherCount: json['otherCount'] as int,
      groupLabTests: groupLabTests,
    );
  }
}

/// Детальная информация о лабораторном анализе с историей
class LabTestDetail {
  final LabTest current;
  final List<LabTestHistoryItem> history;

  LabTestDetail({
    required this.current,
    required this.history,
  });

  factory LabTestDetail.fromJson(Map<String, dynamic> json) {
    final historyJson = json['history'] as List<dynamic>;
    final history = historyJson
        .map((item) => LabTestHistoryItem.fromJson(item as Map<String, dynamic>))
        .toList();

    return LabTestDetail(
      current: LabTest.fromJson(json['current'] as Map<String, dynamic>),
      history: history,
    );
  }
}
