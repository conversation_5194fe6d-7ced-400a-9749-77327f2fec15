import 'package:flutter/material.dart';
import '../medstata_api.dart';

class LabTestDetailDialog extends StatefulWidget {
  final LabTest labTest;

  const LabTestDetailDialog({
    super.key,
    required this.labTest,
  });

  @override
  State<LabTestDetailDialog> createState() => _LabTestDetailDialogState();
}

class _LabTestDetailDialogState extends State<LabTestDetailDialog> {
  final LabTestService _labTestService = LabTestService();
  LabTestDetail? _testDetail;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadTestDetail();
  }

  @override
  void dispose() {
    _labTestService.dispose();
    super.dispose();
  }

  Future<void> _loadTestDetail() async {
    try {
      final response = await _labTestService.getLabTest(widget.labTest.id);
      
      if (response.isSuccess) {
        setState(() {
          _testDetail = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response.message ?? 'Не удалось загрузить детали анализа';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Ошибка при загрузке: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            Expanded(child: _buildContent()),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.science,
          color: Colors.blue.shade600,
          size: 24,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            widget.labTest.name,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Загрузка деталей анализа...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.red.shade700),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCurrentTestInfo(),
          if (_testDetail?.history.isNotEmpty == true) ...[
            const SizedBox(height: 24),
            _buildHistorySection(),
          ],
        ],
      ),
    );
  }

  Widget _buildCurrentTestInfo() {
    final test = _testDetail?.current ?? widget.labTest;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Текущий результат',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade800,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Значение', '${test.value.value} ${test.value.unitLabel}'),
            if (test.value.referenceRange != null)
              _buildInfoRow('Референсный диапазон', test.value.referenceRange!),
            _buildInfoRow('Статус', _getStatusText(test.status), _getStatusColor(test.status)),
            _buildInfoRow('Дата анализа', test.performedAt),
            if (test.interpretationReasons.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Возможные причины:',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              ...test.interpretationReasons.map((reason) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 2),
                child: Text('• $reason'),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHistorySection() {
    final history = _testDetail!.history;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'История анализов (${history.length})',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade800,
              ),
            ),
            const SizedBox(height: 12),
            ...history.map((item) => _buildHistoryItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryItem(LabTestHistoryItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getStatusColor(item.status),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(item.performedAt),
          ),
          Text(
            '${item.value.value} ${item.value.unitLabel}',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(item.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getStatusText(item.status),
              style: TextStyle(
                fontSize: 12,
                color: _getStatusColor(item.status),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor,
                fontWeight: valueColor != null ? FontWeight.w500 : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Закрыть'),
          ),
        ],
      ),
    );
  }

  String _getStatusText(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return 'Норма';
      case LabTestStatus.elevated:
        return 'Повышен';
      case LabTestStatus.low:
        return 'Понижен';
      case LabTestStatus.elevated:
      case LabTestStatus.low:
        return 'Отклонение';
      case LabTestStatus.other:
        return 'Прочее';
    }
  }

  Color _getStatusColor(LabTestStatus status) {
    switch (status) {
      case LabTestStatus.normal:
        return Colors.green;
      case LabTestStatus.elevated:
      case LabTestStatus.low:
      case LabTestStatus.elevated:
      case LabTestStatus.low:
        return Colors.orange;
      case LabTestStatus.other:
        return Colors.blue;
    }
  }
}
