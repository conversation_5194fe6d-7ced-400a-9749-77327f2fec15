import 'package:flutter/material.dart';
import '../medstata_api.dart';
import '../services/auth_state_service.dart';
import '../screens/auth_screen.dart';

/// Виджет-обертка для управления авторизацией
class AuthWrapper extends StatefulWidget {
  final Widget child;
  final Widget? loadingWidget;
  final Widget? errorWidget;

  const AuthWrapper({
    super.key,
    required this.child,
    this.loadingWidget,
    this.errorWidget,
  });

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final AuthService _authService = AuthService();
  final TelegramService _telegramService = TelegramService.instance;
  final AppleService _appleService = AppleService.instance;
  final AuthStateService _authStateService = AuthStateService();

  bool _isLoading = true;
  bool _isAuthenticated = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Подписываемся на изменения состояния авторизации
    _authStateService.addListener(_onAuthStateChanged);

    _initializeAuth();
  }

  @override
  void dispose() {
    _authStateService.removeListener(_onAuthStateChanged);
    super.dispose();
  }

  /// Обработчик изменения состояния авторизации
  void _onAuthStateChanged() {
    final newAuthState = _authStateService.isAuthenticated;
    print('🔄 AuthWrapper: Состояние авторизации изменилось: $_isAuthenticated -> $newAuthState');

    if (_isAuthenticated != newAuthState) {
      setState(() {
        _isAuthenticated = newAuthState;
        _isLoading = false; // Убираем загрузку при изменении состояния
      });

      if (!newAuthState) {
        print('🚪 AuthWrapper: Пользователь разлогинен, показываем экран авторизации');
      } else {
        print('✅ AuthWrapper: Пользователь авторизован, показываем основное приложение');
      }
    }
  }

  Future<void> _initializeAuth() async {
    print('🔐 AuthWrapper: Проверяем статус авторизации');

    // Детальная проверка токенов
    final accessToken = _authService.accessToken;
    final refreshToken = _authService.refreshToken;
    final userId = _authService.userId;
    final isAuthenticated = _authService.isAuthenticated;

    print('🔍 AuthWrapper: Детальная проверка:');
    print('   Access Token: ${accessToken != null ? "${accessToken.substring(0, 20)}..." : "НЕТ"}');
    print('   Refresh Token: ${refreshToken != null ? "${refreshToken.substring(0, 20)}..." : "НЕТ"}');
    print('   User ID: ${userId ?? "НЕТ"}');
    print('   isAuthenticated: $isAuthenticated');

    // Синхронизируем состояние AuthStateService с AuthService
    _authStateService.setAuthenticationStatus(isAuthenticated);

    // Проверяем, есть ли уже сохраненные токены
    if (isAuthenticated) {
      print('✅ AuthWrapper: Пользователь уже авторизован');
      setState(() {
        _isAuthenticated = true;
        _isLoading = false;
      });
      return;
    }

    // Если не авторизован, показываем экран авторизации
    print('❌ AuthWrapper: Пользователь не авторизован, показываем экран входа');
    setState(() {
      _isAuthenticated = false;
      _isLoading = false;
    });
  }

  void _onAuthSuccess() {
    print('✅ AuthWrapper: Авторизация успешна');

    // Проверяем, что токены действительно установлены
    final accessToken = _authService.accessToken;
    final refreshToken = _authService.refreshToken;
    final userId = _authService.userId;

    print('🔍 AuthWrapper: Проверяем токены после авторизации:');
    print('   Access Token: ${accessToken != null ? "${accessToken.substring(0, 20)}..." : "НЕТ"}');
    print('   Refresh Token: ${refreshToken != null ? "${refreshToken.substring(0, 20)}..." : "НЕТ"}');
    print('   User ID: ${userId ?? "НЕТ"}');

    if (accessToken == null || refreshToken == null || userId == null) {
      print('❌ AuthWrapper: Токены не установлены после авторизации!');
      return;
    }

    // Принудительно обновляем состояние авторизации
    print('✅ AuthWrapper: Токены установлены, обновляем состояние');
    _authStateService.setAuthenticationStatus(true);
  }



  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingWidget ?? _buildDefaultLoading();
    }

    if (_errorMessage != null) {
      return widget.errorWidget ?? _buildDefaultError();
    }

    if (!_isAuthenticated) {
      return _buildAuthRequired();
    }

    return widget.child;
  }

  Widget _buildDefaultLoading() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Инициализация...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultError() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'Ошибка инициализации',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.red.shade600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _initializeAuth,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Повторить'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAuthRequired() {
    return AuthScreen(
      onAuthSuccess: _onAuthSuccess,
    );
  }

}
