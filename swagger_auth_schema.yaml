openapi: 3.0.3
info:
  title: MedStata Auth API
  description: API для авторизации в приложении MedStata
  version: 1.0.0

paths:
  /api/auth/telegram:
    post:
      summary: Авторизация через Telegram WebApp
      description: Выполняет авторизацию пользователя через Telegram WebApp
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - first_name
                - auth_date
                - hash
              properties:
                id:
                  type: integer
                  description: ID пользователя в Telegram
                  example: 52952911
                first_name:
                  type: string
                  description: Имя пользователя
                  example: "<PERSON>"
                last_name:
                  type: string
                  description: Фамилия пользователя
                  example: "Doe"
                username:
                  type: string
                  description: Username в Telegram
                  example: "johndoe"
                auth_date:
                  type: integer
                  description: Время авторизации (Unix timestamp)
                  example: 1752325448
                hash:
                  type: string
                  description: Хеш для проверки подлинности данных
                  example: "a1b2c3d4e5f6"
      responses:
        '200':
          description: Успешная авторизация
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Неверные данные запроса
        '401':
          description: Ошибка авторизации

  /api/auth/apple:
    post:
      summary: Авторизация через Apple Sign In
      description: Выполняет авторизацию пользователя через Apple Sign In
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - identityToken
                - authorizationCode
              properties:
                identityToken:
                  type: string
                  description: JWT токен от Apple
                  example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
                authorizationCode:
                  type: string
                  description: Код авторизации от Apple
                  example: "c1234567890abcdef"
                email:
                  type: string
                  description: Email пользователя (если доступен)
                  example: "<EMAIL>"
                firstName:
                  type: string
                  description: Имя пользователя (если доступно)
                  example: "John"
                lastName:
                  type: string
                  description: Фамилия пользователя (если доступна)
                  example: "Doe"
                userIdentifier:
                  type: string
                  description: Уникальный идентификатор пользователя Apple
                  example: "001234.567890abcdef.1234"
      responses:
        '200':
          description: Успешная авторизация
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Неверные данные запроса
        '401':
          description: Ошибка авторизации

  /api/auth/refresh:
    post:
      summary: Обновление access токена
      description: Обновляет access токен используя refresh токен
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  description: Refresh токен для обновления access токена
                  example: "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJtZWRzdGF0YSI..."
      responses:
        '200':
          description: Токен успешно обновлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '400':
          description: Неверные данные запроса
        '401':
          description: Невалидный или просроченный refresh токен

components:
  schemas:
    AuthResponse:
      type: object
      required:
        - status
        - userId
        - accessToken
        - refreshToken
        - accessTokenExpiresAt
        - refreshTokenExpiresAt
      properties:
        status:
          type: string
          description: Статус операции
          example: "ok"
        userId:
          type: string
          description: Уникальный ID пользователя
          example: "724398c2-bb3e-4072-9a05-7849d3647d8e"
        accessToken:
          type: string
          description: JWT access токен для API запросов
          example: "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJtZWRzdGF0YSI..."
        refreshToken:
          type: string
          description: Refresh токен для обновления access токена
          example: "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJtZWRzdGF0YSI..."
        accessTokenExpiresAt:
          type: integer
          description: Время истечения access токена (Unix timestamp в секундах)
          example: 1752326348
        refreshTokenExpiresAt:
          type: integer
          description: Время истечения refresh токена (Unix timestamp в секундах)
          example: 1754917448

    RefreshTokenResponse:
      type: object
      required:
        - status
        - accessToken
        - refreshToken
        - accessTokenExpiresAt
        - refreshTokenExpiresAt
      properties:
        status:
          type: string
          description: Статус операции
          example: "ok"
        accessToken:
          type: string
          description: Новый JWT access токен для API запросов
          example: "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJtZWRzdGF0YSI..."
        refreshToken:
          type: string
          description: Новый refresh токен для следующих обновлений
          example: "eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJtZWRzdGF0YSI..."
        accessTokenExpiresAt:
          type: integer
          description: Время истечения нового access токена (Unix timestamp в секундах)
          example: 1752326348
        refreshTokenExpiresAt:
          type: integer
          description: Время истечения нового refresh токена (Unix timestamp в секундах)
          example: 1754917448

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []
